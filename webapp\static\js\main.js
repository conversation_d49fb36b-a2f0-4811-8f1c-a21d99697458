/**
 * Exam Grader Web Application
 * Main JavaScript file
 */

document.addEventListener("DOMContentLoaded", function () {
  // Note: File upload handling is now done in individual page templates
  // to avoid conflicts and provide better control

  // Handle confirmation dialogs
  const confirmButtons = document.querySelectorAll("[data-confirm]");
  confirmButtons.forEach((button) => {
    button.addEventListener("click", (e) => {
      const message = button.getAttribute("data-confirm");
      if (!confirm(message)) {
        e.preventDefault();
      }
    });
  });

  // Show loading overlay on form submissions
  const forms = document.querySelectorAll("form:not(.no-loading)");
  forms.forEach((form) => {
    form.addEventListener("submit", () => {
      const loadingOverlay = document.getElementById("loadingOverlay");
      if (loadingOverlay) {
        loadingOverlay.classList.remove("d-none");
        loadingOverlay.classList.add("d-flex");
      }
    });
  });

  // Dismiss alerts automatically after 5 seconds
  const alerts = document.querySelectorAll(".alert:not(.alert-permanent)");
  alerts.forEach((alert) => {
    setTimeout(() => {
      const closeButton = alert.querySelector(".btn-close");
      if (closeButton) {
        closeButton.click();
      }
    }, 5000);
  });

  // Save session data to localStorage when navigating away
  window.addEventListener("beforeunload", function () {
    // Get session data from the page
    const sessionData = {};

    // Check for guide content
    const guideContent = document.querySelector(".guide-content");
    if (guideContent) {
      sessionData.guideContent = guideContent.textContent;
    }

    // Check for submission content
    const submissionContent = document.querySelector(".submission-content");
    if (submissionContent) {
      sessionData.submissionContent = submissionContent.textContent;
    }

    // Check for mapping result
    const mappingResult = document.querySelector("#mappingResult");
    if (mappingResult) {
      sessionData.mappingResult = mappingResult.textContent;
    }

    // Check for grading result
    const gradingResult = document.querySelector("#gradingResult");
    if (gradingResult) {
      sessionData.gradingResult = gradingResult.textContent;
    }

    // Save to localStorage if we have data
    if (Object.keys(sessionData).length > 0) {
      localStorage.setItem(
        "examGraderSessionData",
        JSON.stringify(sessionData)
      );
    }
  });

  // Restore session data from localStorage when page loads
  const sessionData = localStorage.getItem("examGraderSessionData");
  if (sessionData) {
    try {
      const data = JSON.parse(sessionData);

      // Restore guide content if needed
      if (data.guideContent) {
        const guideContent = document.querySelector(".guide-content");
        if (guideContent && !guideContent.textContent.trim()) {
          guideContent.textContent = data.guideContent;
        }
      }

      // Restore submission content if needed
      if (data.submissionContent) {
        const submissionContent = document.querySelector(".submission-content");
        if (submissionContent && !submissionContent.textContent.trim()) {
          submissionContent.textContent = data.submissionContent;
        }
      }

      // Restore mapping result if needed
      if (data.mappingResult) {
        const mappingResult = document.querySelector("#mappingResult");
        if (mappingResult && !mappingResult.textContent.trim()) {
          mappingResult.textContent = data.mappingResult;
        }
      }

      // Restore grading result if needed
      if (data.gradingResult) {
        const gradingResult = document.querySelector("#gradingResult");
        if (gradingResult && !gradingResult.textContent.trim()) {
          gradingResult.textContent = data.gradingResult;
        }
      }
    } catch (e) {
      console.error("Error restoring session data:", e);
    }
  }
});
