{"elements": {"forms": [{"index": 1, "attributes": "action=\"/upload_guide\" method=\"post\" enctype=\"multipart/form-data\" id=\"guideForm\"", "content": "<label for=\"guideFile\" class=\"upload-dropzone\" id=\"guideDropzone\">\n                                <input type=\"file\" id=\"guideFile\" name=\"file\" accept=\".docx,.txt\" style=\"display: none;\">\n           ..."}, {"index": 2, "attributes": "action=\"/upload_submission\" method=\"post\" enctype=\"multipart/form-data\" id=\"submissionForm\"", "content": "<label for=\"submissionFile\" class=\"upload-dropzone\" id=\"submissionDropzone\">\n                                <input type=\"file\" id=\"submissionFile\" name=\"file\" accept=\".docx,.txt,.pdf,.jpg,.jpeg,.png,..."}, {"index": 3, "attributes": "action=\"/grade_submission\" method=\"post\" class=\"processing-form-compact\"", "content": "<div class=\"form-group-compact mb-3\">\n                            <label>Questions to Answer</label>\n                            <input type=\"number\" class=\"form-control-compact\" name=\"num_questions\" ..."}, {"index": 4, "attributes": "action=\"/clear_all\" method=\"post\" id=\"clearAllForm\"", "content": "<button type=\"submit\" class=\"btn-warning-compact\" id=\"clearAllButton\"\n                                    data-confirm=\"Clear all data and cache files? This cannot be undone.\"\n                        ..."}], "buttons": [{"type": "button_element", "index": 1, "attributes": "class=\"menu-toggle\"", "content": "<i class=\"bi bi-list\"></i>"}, {"type": "button_element", "index": 2, "attributes": "class=\"btn-icon\" id=\"notificationsBtn\"", "content": "<i class=\"bi bi-bell\"></i>"}, {"type": "button_element", "index": 3, "attributes": "class=\"btn-icon\" id=\"userProfileBtn\"", "content": "<i class=\"bi bi-person-circle\"></i>"}, {"type": "button_element", "index": 4, "attributes": "type=\"submit\" class=\"btn-primary-compact\"\n                                    disabled", "content": "<i class=\"bi bi-play-circle\"></i>\n                                Process & Grade..."}, {"type": "button_element", "index": 5, "attributes": "type=\"submit\" class=\"btn-warning-compact\" id=\"clearAllButton\"\n                                    data-confirm=\"Clear all data and cache files? This cannot be undone.\"\n                                    data-loading-text=\"Clearing...\"", "content": "<i class=\"bi bi-trash\"></i> Clear All Data..."}], "inputs": [{"index": 1, "attributes": "type=\"file\" id=\"guideFile\" name=\"file\" accept=\".docx,.txt\" style=\"display: none;\""}, {"index": 2, "attributes": "type=\"file\" id=\"submissionFile\" name=\"file\" accept=\".docx,.txt,.pdf,.jpg,.jpeg,.png,.tiff,.bmp,.gif\" multiple style=\"display: none;\""}, {"index": 3, "attributes": "type=\"number\" class=\"form-control-compact\" name=\"num_questions\" min=\"1\" value=\"1\""}], "selects": [], "labels": [{"index": 1, "attributes": "for=\"guideFile\" class=\"upload-dropzone\" id=\"guideDropzone\"", "content": "<input type=\"file\" id=\"guideFile\" name=\"file\" accept=\".docx,.txt\" style=\"display: none;\">\n          ..."}, {"index": 2, "attributes": "for=\"submissionFile\" class=\"upload-dropzone\" id=\"submissionDropzone\"", "content": "<input type=\"file\" id=\"submissionFile\" name=\"file\" accept=\".docx,.txt,.pdf,.jpg,.jpeg,.png,.tiff,.bm..."}, {"index": 3, "attributes": "", "content": "Questions to Answer"}]}, "issues": [{"category": "FORMS", "severity": "warning", "message": "Form 3 missing ID attribute", "element": " action=\"/grade_submission\" method=\"post\" class=\"processing-form-compact\"", "details": null}, {"category": "BUTTONS", "severity": "warning", "message": "Button 1 missing type attribute", "element": " class=\"menu-toggle\"", "details": null}, {"category": "BUTTONS", "severity": "warning", "message": "Button 1 may lack event handling", "element": " class=\"menu-toggle\"", "details": null}, {"category": "BUTTONS", "severity": "warning", "message": "Button 2 missing type attribute", "element": " class=\"btn-icon\" id=\"notificationsBtn\"", "details": null}, {"category": "BUTTONS", "severity": "warning", "message": "Button 2 may lack event handling", "element": " class=\"btn-icon\" id=\"notificationsBtn\"", "details": null}, {"category": "BUTTONS", "severity": "warning", "message": "Button 3 missing type attribute", "element": " class=\"btn-icon\" id=\"userProfileBtn\"", "details": null}, {"category": "BUTTONS", "severity": "warning", "message": "Button 3 may lack event handling", "element": " class=\"btn-icon\" id=\"userProfileBtn\"", "details": null}, {"category": "BUTTONS", "severity": "info", "message": "Button 4 is statically disabled", "element": " type=\"submit\" class=\"btn-primary-compact\"\n                                    disabled", "details": null}, {"category": "LABELS", "severity": "warning", "message": "Label 3 missing 'for' attribute", "element": "", "details": null}], "summary": {"total_forms": 4, "total_buttons": 5, "total_inputs": 3, "total_selects": 0, "total_labels": 3, "total_issues": 9, "critical_issues": 0}}