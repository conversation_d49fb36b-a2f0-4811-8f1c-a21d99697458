{% extends "base.html" %}

{% block title %}Exam Grader - Dashboard{% endblock %}

{% block header %}Dashboard{% endblock %}

{% block content %}
<!-- Dashboard Cards Grid -->
<div class="dashboard-grid">
    <!-- Stats Cards - Two Rows, Three Columns Each -->
    <!-- First Row -->
    <div class="row g-3 mb-4">
        <div class="col-lg-4 col-md-6">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-journal-text"></i>
                </div>
                <div class="stat-content">
                    <h3>Guide Status</h3>
                    {% if session.get('guide_uploaded') %}
                    <div class="stat-value stat-success">Ready</div>
                    <div class="stat-label">{{ "%.1f"|format(session.get('guide_content', '')|length / 1000) }}k chars</div>
                    {% else %}
                    <div class="stat-value stat-muted">Not uploaded</div>
                    <div class="stat-label">Upload required</div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-files"></i>
                </div>
                <div class="stat-content">
                    <h3>Submissions</h3>
                    <div class="stat-value">{{ session.get('submissions', [])|length or (1 if session.get('last_submission') else 0) }}</div>
                    <div class="stat-label">Files uploaded</div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-award"></i>
                </div>
                <div class="stat-content">
                    <h3>Last Score</h3>
                    {% if session.get('last_score') %}
                    <div class="stat-value stat-primary">{{ session.get('last_score', 0) }}%</div>
                    <div class="stat-label">Recent grade</div>
                    {% else %}
                    <div class="stat-value stat-muted">--</div>
                    <div class="stat-label">No grades yet</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Second Row -->
    <div class="row g-3 mb-4">
        <div class="col-lg-4 col-md-6">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-robot"></i>
                </div>
                <div class="stat-content">
                    <h3>AI Status</h3>
                    {% if llm_status %}
                    <div class="stat-value stat-success">Online</div>
                    <div class="stat-label">Ready to grade</div>
                    {% else %}
                    <div class="stat-value stat-danger">Offline</div>
                    <div class="stat-label">Service unavailable</div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-clock-history"></i>
                </div>
                <div class="stat-content">
                    <h3>Cache Usage</h3>
                    {% if storage_stats %}
                    <div class="stat-value">{{ "%.0f"|format(storage_stats.total_size_mb) }}MB</div>
                    <div class="stat-label">Storage used</div>
                    {% else %}
                    <div class="stat-value stat-muted">0MB</div>
                    <div class="stat-label">Storage used</div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-activity"></i>
                </div>
                <div class="stat-content">
                    <h3>System Status</h3>
                    {% if session.get('last_grading_result') or session.get('batch_results') %}
                    <div class="stat-value stat-success">Active</div>
                    <div class="stat-label">Results available</div>
                    {% else %}
                    <div class="stat-value stat-muted">Idle</div>
                    <div class="stat-label">No recent activity</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Row -->
    <div class="row g-3 mb-4">
        <!-- Upload Section -->
        <div class="col-lg-5">
            <div class="main-card compact-card">
                <div class="card-header">
                    <h4><i class="bi bi-cloud-upload"></i> Upload Files</h4>
                    <p class="card-subtitle">Upload guide and submissions</p>
                </div>
                <div class="card-content">
                    <!-- Guide Upload -->
                    <div class="upload-section mb-3">
                        <div class="upload-header">
                            <div class="upload-title">
                                <i class="bi bi-journal-text text-primary"></i>
                                <h5>Marking Guide</h5>
                            </div>
                            {% if session.get('guide_uploaded') %}
                            <span class="status-badge success">
                                <i class="bi bi-check-circle"></i> Uploaded
                            </span>
                            {% else %}
                            <span class="status-badge required">
                                <i class="bi bi-exclamation-circle"></i> Required
                            </span>
                            {% endif %}
                        </div>

                        <form action="{{ url_for('upload_guide') }}" method="post" enctype="multipart/form-data" id="guideForm">
                            <label for="guideFile" class="upload-dropzone" id="guideDropzone">
                                <input type="file" id="guideFile" name="file" accept=".docx,.txt" style="display: none;">
                                <div class="upload-content">
                                    <div class="upload-icon">
                                        <i class="bi bi-cloud-upload"></i>
                                    </div>
                                    <div class="upload-text">
                                        <h6>Drop your marking guide here</h6>
                                        <p>or <span class="upload-link">click anywhere to browse</span></p>
                                        <small class="upload-formats">Supports: .docx, .txt files</small>
                                    </div>
                                </div>
                                <div class="upload-progress" style="display: none;">
                                    <div class="progress-bar"></div>
                                    <span class="progress-text">Uploading...</span>
                                </div>
                            </label>
                        </form>

                        {% if session.get('guide_uploaded') %}
                        <div class="upload-success">
                            <div class="success-info">
                                <i class="bi bi-check-circle-fill text-success"></i>
                                <div>
                                    <strong>Guide uploaded successfully</strong>
                                    <small>{{ "%.1f"|format(session.get('guide_content', '')|length / 1000) }}k characters extracted</small>
                                </div>
                            </div>
                            <div class="upload-actions">
                                <a href="{{ url_for('view_guide') }}" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye"></i> Preview
                                </a>
                                <form action="{{ url_for('clear_guide') }}" method="post" style="display: inline;">
                                    <button type="submit" class="btn btn-outline-danger btn-sm" data-confirm="Remove this guide?">
                                        <i class="bi bi-trash"></i> Remove
                                    </button>
                                </form>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Submissions Upload -->
                    <div class="upload-section">
                        <div class="upload-header">
                            <div class="upload-title">
                                <i class="bi bi-files text-success"></i>
                                <h5>Student Submissions</h5>
                            </div>
                            {% if session.get('submissions') or session.get('last_submission') %}
                            <span class="status-badge success">
                                <i class="bi bi-check-circle"></i> {{ session.get('submissions', [])|length or 1 }} files
                            </span>
                            {% else %}
                            <span class="status-badge optional">
                                <i class="bi bi-upload"></i> Upload files
                            </span>
                            {% endif %}
                        </div>

                        <form action="{{ url_for('upload_submission') }}" method="post" enctype="multipart/form-data" id="submissionForm">
                            <label for="submissionFile" class="upload-dropzone" id="submissionDropzone">
                                <input type="file" id="submissionFile" name="file" accept=".docx,.txt,.pdf,.jpg,.jpeg,.png,.tiff,.bmp,.gif" multiple style="display: none;">
                                <div class="upload-content">
                                    <div class="upload-icon">
                                        <i class="bi bi-file-earmark-plus"></i>
                                    </div>
                                    <div class="upload-text">
                                        <h6>Drop student submissions here</h6>
                                        <p>or <span class="upload-link">click anywhere to browse</span></p>
                                        <small class="upload-formats">Supports: PDF, Word, Text, Images • Multiple files allowed</small>
                                    </div>
                                </div>
                                <div class="upload-progress" style="display: none;">
                                    <div class="progress-bar"></div>
                                    <span class="progress-text">Uploading files...</span>
                                </div>
                            </label>
                        </form>

                        {% if session.get('submissions') or session.get('last_submission') %}
                        <div class="upload-success">
                            <div class="success-info">
                                <i class="bi bi-check-circle-fill text-success"></i>
                                <div>
                                    <strong>{{ session.get('submissions', [])|length or 1 }} submission(s) uploaded</strong>
                                    <small>Ready for processing and grading</small>
                                </div>
                            </div>
                            <div class="upload-actions">
                                <a href="{{ url_for('view_submission') }}" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye"></i> View All
                                </a>
                                <form action="{{ url_for('clear_submission') }}" method="post" style="display: inline;">
                                    <button type="submit" class="btn btn-outline-danger btn-sm" data-confirm="Remove all submissions?">
                                        <i class="bi bi-trash"></i> Clear All
                                    </button>
                                </form>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Processing Section -->
        <div class="col-lg-4">
            <div class="main-card compact-card">
                <div class="card-header">
                    <h4><i class="bi bi-robot"></i> AI Processing</h4>
                    <p class="card-subtitle">Grade with AI analysis</p>
                </div>
                <div class="card-content">
                    <form action="{{ url_for('grade_submission') }}" method="post" class="processing-form-compact">
                        <div class="form-group-compact mb-3">
                            <label>Questions to Answer</label>
                            <input type="number" class="form-control-compact" name="num_questions" min="1" value="1">
                        </div>

                        {% if session.get('submissions') and session.get('submissions')|length > 1 %}
                        <div class="form-group-compact mb-3">
                            <label>Select Submission</label>
                            <select class="form-control-compact" name="submission_id">
                                {% for sub in session.get('submissions', []) %}
                                <option value="{{ sub.filename }}">{{ sub.filename }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        {% endif %}

                        <div class="processing-actions-compact">
                            <button type="submit" class="btn-primary-compact"
                                    {{ 'disabled' if not session.get('guide_uploaded') or (not session.get('last_submission') and not session.get('submissions')) }}>
                                <i class="bi bi-play-circle"></i>
                                Process & Grade
                            </button>
                        </div>
                    </form>

                    {% if session.get('submissions') and session.get('submissions')|length > 1 %}
                    <div class="batch-section-compact mt-3">
                        <form action="{{ url_for('batch_grade') }}" method="post">
                            <input type="hidden" name="num_questions" value="1">
                            <button type="submit" class="btn-success-compact"
                                    {{ 'disabled' if not session.get('guide_uploaded') or session.get('submissions')|length < 2 }}>
                                <i class="bi bi-layers"></i>
                                Process All
                            </button>
                        </form>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Results & Management Section -->
        <div class="col-lg-3">
            <div class="main-card compact-card">
                <div class="card-header">
                    <h4><i class="bi bi-graph-up"></i> Results</h4>
                    <p class="card-subtitle">View and manage</p>
                </div>
                <div class="card-content">
                    {% if session.get('last_grading_result') or session.get('batch_results') %}
                    <div class="results-section-compact">
                        <div class="results-grid-compact">
                            {% if session.get('last_grading_result') %}
                            <a href="{{ url_for('view_results') }}" class="result-card-compact">
                                <i class="bi bi-file-text"></i>
                                <span>View Results</span>
                            </a>
                            {% endif %}
                            {% if session.get('batch_results') %}
                            <a href="{{ url_for('view_batch_results') }}" class="result-card-compact">
                                <i class="bi bi-collection"></i>
                                <span>Batch Results</span>
                            </a>
                            {% endif %}
                        </div>
                    </div>
                    {% else %}
                    <div class="no-results-compact">
                        <i class="bi bi-graph-up-arrow"></i>
                        <p>No results yet</p>
                        <small>Process submissions to see results</small>
                    </div>
                    {% endif %}

                    <div class="management-section-compact mt-4">
                        <form action="{{ url_for('clear_all') }}" method="post" id="clearAllForm">
                            <button type="submit" class="btn-warning-compact" id="clearAllButton"
                                    data-confirm="Clear all data and cache files? This cannot be undone."
                                    data-loading-text="Clearing...">
                                <i class="bi bi-trash"></i> Clear All Data
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Loaders are now handled by the loader.js component -->
{% endblock %}

{% block extra_css %}
<style>
    /* Compact Upload UX Styles */
    .upload-section {
        margin-bottom: 1.5rem;
    }

    .upload-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid var(--gray-200);
    }

    .upload-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .upload-title h5 {
        margin: 0;
        font-weight: 600;
        color: var(--gray-800);
    }

    .upload-title i {
        font-size: 1.2rem;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .status-badge.success {
        background: #d1fae5;
        color: #065f46;
        border: 1px solid #a7f3d0;
    }

    .status-badge.required {
        background: #fef3c7;
        color: #92400e;
        border: 1px solid #fcd34d;
    }

    .status-badge.optional {
        background: #e0e7ff;
        color: #3730a3;
        border: 1px solid #c7d2fe;
    }

    .upload-dropzone {
        border: 2px dashed var(--gray-300);
        border-radius: 12px;
        padding: 2rem 1rem;
        text-align: center;
        background: var(--gray-50);
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        min-height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: auto !important;
        user-select: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        z-index: 1;
    }

    .upload-dropzone:hover {
        border-color: var(--primary-blue);
        background: var(--primary-blue-50);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
        border-style: solid;
    }

    .upload-dropzone:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        border-color: var(--primary-blue-dark);
    }

    .upload-dropzone.dragover {
        border-color: var(--primary-blue);
        background: var(--primary-blue-100);
        transform: scale(1.02);
    }

    .upload-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        /* REMOVED: pointer-events: none; - This was blocking clicks! */
    }

    .upload-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: var(--primary-blue-100);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 0.25rem;
    }

    .upload-icon i {
        font-size: 1rem;
        color: var(--primary-blue);
    }

    .upload-text h6 {
        margin: 0 0 0.25rem 0;
        font-weight: 600;
        color: var(--gray-800);
        font-size: 0.9rem;
    }

    .upload-text p {
        margin: 0 0 0.25rem 0;
        color: var(--gray-600);
        font-size: 0.85rem;
    }

    .upload-link {
        color: var(--primary-blue);
        font-weight: 500;
        text-decoration: underline;
        cursor: pointer;
    }

    .upload-link:hover {
        color: var(--primary-blue-dark);
    }

    .upload-formats {
        color: var(--gray-500);
        font-size: 0.75rem;
    }

    .upload-progress {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 12px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .progress-bar {
        width: 200px;
        height: 4px;
        background: var(--gray-200);
        border-radius: 2px;
        overflow: hidden;
        position: relative;
    }

    .progress-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, var(--primary-blue), var(--primary-blue-light));
        animation: progress 1.5s infinite;
    }

    @keyframes progress {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .progress-text {
        color: var(--primary-blue);
        font-weight: 500;
    }

    .upload-success {
        background: #f0fdf4;
        border: 1px solid #bbf7d0;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .success-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .success-info i {
        font-size: 1.2rem;
    }

    .success-info strong {
        display: block;
        color: var(--gray-800);
        margin-bottom: 0.25rem;
    }

    .success-info small {
        color: var(--gray-600);
        font-size: 0.8rem;
    }

    .upload-actions {
        display: flex;
        gap: 0.5rem;
    }

    /* File validation feedback */
    .upload-dropzone.invalid {
        border-color: #ef4444;
        background: #fef2f2;
    }

    .upload-dropzone.invalid .upload-icon {
        background: #fee2e2;
    }

    .upload-dropzone.invalid .upload-icon i {
        color: #ef4444;
    }

    .file-error {
        background: #fef2f2;
        border: 1px solid #fecaca;
        border-radius: 8px;
        padding: 0.75rem;
        margin-top: 0.5rem;
        color: #dc2626;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .upload-dropzone {
            padding: 1.5rem 1rem;
            min-height: 100px;
        }

        .upload-content {
            gap: 0.75rem;
        }

        .upload-icon {
            width: 40px;
            height: 40px;
        }

        .upload-icon i {
            font-size: 1.2rem;
        }

        .upload-text h6 {
            font-size: 0.9rem;
        }

        .upload-success {
            flex-direction: column;
            gap: 1rem;
            align-items: flex-start;
        }

        .upload-actions {
            width: 100%;
            justify-content: flex-end;
        }
    }

    /* Dashboard specific styles */
    .submission-list {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid var(--gray-200);
        border-radius: var(--border-radius);
        background: var(--gray-50);
    }

    .submission-item {
        transition: var(--transition);
        padding: 0.75rem;
        border-bottom: 1px solid var(--gray-200);
    }

    .submission-item:hover {
        background-color: rgba(79, 70, 229, 0.05);
    }

    .submission-item:last-child {
        border-bottom: none !important;
    }

    .submission-filename {
        font-weight: 500;
        color: var(--gray-700);
    }

    /* Enhanced card hover effects */
    .card:hover .card-header h5 i {
        transform: scale(1.1);
        transition: var(--transition);
    }

    /* Progress indicator styling */
    .progress {
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    /* Welcome section gradient text fallback */
    .display-4 {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        color: transparent;
    }

    /* Status card enhancements */
    .status-card h6 {
        color: var(--gray-700);
        font-weight: 600;
    }

    /* Button group improvements */
    .btn-lg {
        padding: 1rem 2rem;
        font-size: 1rem;
        font-weight: 600;
    }

    /* Processing section styling */
    .card .row .col-md-6:first-child {
        border-right: 1px solid var(--gray-200);
    }

    @media (max-width: 768px) {
        .card .row .col-md-6:first-child {
            border-right: none;
            border-bottom: 1px solid var(--gray-200);
            padding-bottom: 1rem;
            margin-bottom: 1rem;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Enhanced Upload UX with Drag & Drop
    document.addEventListener('DOMContentLoaded', function() {
        // Simplified loading function
        function showLoading(text = 'Processing...') {
            console.log('Loading:', text);
            // Just log for now - the form submission will handle the redirect
        }

        // Simplified upload progress function
        function showUploadProgress(dropzone, text = 'Uploading...') {
            console.log('Upload progress:', text);
            // Just log for now - the form submission will handle the redirect
        }

        // Global prevent defaults function
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        // Helper function to validate file
        function validateFile(file, allowedTypes, maxSize = 20 * 1024 * 1024) {
            const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
            const isValidType = allowedTypes.includes(fileExtension);
            const isValidSize = file.size <= maxSize;

            return {
                valid: isValidType && isValidSize,
                error: !isValidType ? `File type ${fileExtension} not supported` :
                       !isValidSize ? `File size too large (max 20MB)` : null
            };
        }

        // Simplified file error function
        function showFileError(dropzone, message) {
            console.error('File error:', message);
            alert('Error: ' + message);
        }

        // Debug function to test upload functionality
        function debugUploadElements() {
            console.log('=== Upload Debug Information ===');

            const guideDropzone = document.getElementById('guideDropzone');
            const guideFileInput = document.getElementById('guideFile');
            const submissionDropzone = document.getElementById('submissionDropzone');
            const submissionFileInput = document.getElementById('submissionFile');

            console.log('Guide elements:', {
                dropzone: guideDropzone,
                fileInput: guideFileInput,
                dropzoneVisible: guideDropzone ? getComputedStyle(guideDropzone).display !== 'none' : false,
                inputVisible: guideFileInput ? getComputedStyle(guideFileInput).display : 'N/A'
            });

            console.log('Submission elements:', {
                dropzone: submissionDropzone,
                fileInput: submissionFileInput,
                dropzoneVisible: submissionDropzone ? getComputedStyle(submissionDropzone).display !== 'none' : false,
                inputVisible: submissionFileInput ? getComputedStyle(submissionFileInput).display : 'N/A'
            });

            // Test file input accessibility
            if (guideFileInput) {
                try {
                    console.log('Testing guide file input click...');
                    guideFileInput.click();
                    console.log('Guide file input click test: SUCCESS');
                } catch (e) {
                    console.error('Guide file input click test: FAILED', e);
                }
            }

            if (submissionFileInput) {
                try {
                    console.log('Testing submission file input click...');
                    submissionFileInput.click();
                    console.log('Submission file input click test: SUCCESS');
                } catch (e) {
                    console.error('Submission file input click test: FAILED', e);
                }
            }
        }

        // Make debug function available globally
        window.debugUploadElements = debugUploadElements;

        // Setup drag and drop for guide upload
        const guideDropzone = document.getElementById('guideDropzone');
        const guideFileInput = document.getElementById('guideFile');
        const guideForm = document.getElementById('guideForm');

        console.log('Guide elements:', {
            dropzone: !!guideDropzone,
            input: !!guideFileInput,
            form: !!guideForm
        });

        if (guideDropzone && guideFileInput && guideForm) {
            console.log('Setting up guide upload handlers');
            const allowedGuideTypes = ['.docx', '.txt'];

            // Note: Click handling is now done via HTML label element
            // The label automatically triggers the file input when clicked
            console.log('Guide upload area is now clickable via label element');

        // Define preventDefaults function for drag and drop
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

            // File input change
            guideFileInput.addEventListener('change', function() {
                console.log('Guide file input changed, files:', this.files.length);
                if (this.files.length > 0) {
                    const file = this.files[0];
                    console.log('Selected file:', file.name, 'size:', file.size);
                    const validation = validateFile(file, allowedGuideTypes);
                    console.log('Validation result:', validation);

                    if (validation.valid) {
                        console.log('File valid, submitting form');
                        showUploadProgress(guideDropzone, 'Uploading marking guide...');
                        showLoading('Processing Marking Guide...');
                        guideForm.submit();
                    } else {
                        console.log('File invalid:', validation.error);
                        showFileError(guideDropzone, validation.error);
                        this.value = ''; // Clear the input
                    }
                }
            });

            // Drag and drop events
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                guideDropzone.addEventListener(eventName, preventDefaults, false);
            });

            ['dragenter', 'dragover'].forEach(eventName => {
                guideDropzone.addEventListener(eventName, () => {
                    guideDropzone.classList.add('dragover');
                }, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                guideDropzone.addEventListener(eventName, () => {
                    guideDropzone.classList.remove('dragover');
                }, false);
            });

            guideDropzone.addEventListener('drop', function(e) {
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    const validation = validateFile(file, allowedGuideTypes);

                    if (validation.valid) {
                        guideFileInput.files = files;
                        showUploadProgress(guideDropzone, 'Uploading marking guide...');
                        showLoading('Processing Marking Guide...');
                        guideForm.submit();
                    } else {
                        showFileError(guideDropzone, validation.error);
                    }
                }
            });
        } else {
            console.error('Guide upload elements not found!', {
                dropzone: guideDropzone,
                input: guideFileInput,
                form: guideForm
            });
        }

        // Setup drag and drop for submission upload
        const submissionDropzone = document.getElementById('submissionDropzone');
        const submissionFileInput = document.getElementById('submissionFile');
        const submissionForm = document.getElementById('submissionForm');

        console.log('Submission elements:', {
            dropzone: !!submissionDropzone,
            input: !!submissionFileInput,
            form: !!submissionForm
        });

        if (submissionDropzone && submissionFileInput && submissionForm) {
            console.log('Setting up submission upload handlers');
            const allowedSubmissionTypes = ['.docx', '.txt', '.pdf', '.jpg', '.jpeg', '.png', '.tiff', '.bmp', '.gif'];

            // Note: Click handling is now done via HTML label element
            // The label automatically triggers the file input when clicked
            console.log('Submission upload area is now clickable via label element');

            // File input change
            submissionFileInput.addEventListener('change', function() {
                console.log('Submission file input changed, files:', this.files.length);
                if (this.files.length > 0) {
                    const files = Array.from(this.files);
                    let allValid = true;
                    let errorMessage = '';

                    for (let file of files) {
                        const validation = validateFile(file, allowedSubmissionTypes);
                        if (!validation.valid) {
                            allValid = false;
                            errorMessage = validation.error;
                            break;
                        }
                    }

                    if (allValid) {
                        console.log('All submission files valid, submitting form');
                        const fileText = files.length === 1 ? 'file' : 'files';
                        showUploadProgress(submissionDropzone, `Uploading ${files.length} ${fileText}...`);
                        showLoading('Processing Submissions...');
                        submissionForm.submit();
                    } else {
                        console.log('Submission file validation failed:', errorMessage);
                        showFileError(submissionDropzone, errorMessage);
                        this.value = ''; // Clear the input
                    }
                }
            });

            // Drag and drop events
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                submissionDropzone.addEventListener(eventName, preventDefaults, false);
            });

            ['dragenter', 'dragover'].forEach(eventName => {
                submissionDropzone.addEventListener(eventName, () => {
                    submissionDropzone.classList.add('dragover');
                }, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                submissionDropzone.addEventListener(eventName, () => {
                    submissionDropzone.classList.remove('dragover');
                }, false);
            });

            submissionDropzone.addEventListener('drop', function(e) {
                const files = Array.from(e.dataTransfer.files);
                let allValid = true;
                let errorMessage = '';

                for (let file of files) {
                    const validation = validateFile(file, allowedSubmissionTypes);
                    if (!validation.valid) {
                        allValid = false;
                        errorMessage = validation.error;
                        break;
                    }
                }

                if (allValid) {
                    submissionFileInput.files = e.dataTransfer.files;
                    const fileText = files.length === 1 ? 'file' : 'files';
                    showUploadProgress(submissionDropzone, `Uploading ${files.length} ${fileText}...`);
                    showLoading('Processing Submissions...');
                    submissionForm.submit();
                } else {
                    showFileError(submissionDropzone, errorMessage);
                }
            });
        } else {
            console.error('Submission upload elements not found!', {
                dropzone: submissionDropzone,
                input: submissionFileInput,
                form: submissionForm
            });
        }

        // Debug: Test if label elements are working
        function testLabelFunctionality() {
            console.log('=== Testing Label Functionality ===');

            const guideLabel = document.querySelector('label[for="guideFile"]');
            const submissionLabel = document.querySelector('label[for="submissionFile"]');
            const guideInput = document.getElementById('guideFile');
            const submissionInput = document.getElementById('submissionFile');

            console.log('Guide label:', guideLabel);
            console.log('Submission label:', submissionLabel);
            console.log('Guide input:', guideInput);
            console.log('Submission input:', submissionInput);

            if (guideLabel && guideInput) {
                console.log('Guide label for attribute:', guideLabel.getAttribute('for'));
                console.log('Guide input id:', guideInput.id);
                console.log('Guide label-input association:', guideLabel.getAttribute('for') === guideInput.id);
            }

            if (submissionLabel && submissionInput) {
                console.log('Submission label for attribute:', submissionLabel.getAttribute('for'));
                console.log('Submission input id:', submissionInput.id);
                console.log('Submission label-input association:', submissionLabel.getAttribute('for') === submissionInput.id);
            }

            // Test manual click
            if (guideInput) {
                console.log('Testing manual guide input click...');
                try {
                    guideInput.click();
                    console.log('Manual guide input click: SUCCESS');
                } catch (e) {
                    console.error('Manual guide input click: FAILED', e);
                }
            }
        }

        // Run test
        testLabelFunctionality();

        // Make test function available globally
        window.testLabelFunctionality = testLabelFunctionality;

        // Add a simple upload test function
        window.testUploadFunctionality = function() {
            console.log('=== Testing Upload Functionality ===');

            // Test guide upload
            console.log('Testing guide upload...');
            if (guideFileInput) {
                try {
                    guideFileInput.click();
                    console.log('✅ Guide upload test: SUCCESS');
                } catch (e) {
                    console.error('❌ Guide upload test: FAILED', e);
                }
            } else {
                console.error('❌ Guide file input not found');
            }

            // Test submission upload
            console.log('Testing submission upload...');
            if (submissionFileInput) {
                try {
                    submissionFileInput.click();
                    console.log('✅ Submission upload test: SUCCESS');
                } catch (e) {
                    console.error('❌ Submission upload test: FAILED', e);
                }
            } else {
                console.error('❌ Submission file input not found');
            }

            console.log('=== Upload Functionality Test Complete ===');
        };

        console.log('Upload areas configured with label elements for native click handling');
        console.log('Run testLabelFunctionality() in console to debug');

        // CRITICAL FIX: Remove all JavaScript click handlers since we're using HTML labels
        // The label element should handle clicks automatically
        console.log('Upload functionality relies on HTML label elements');
        console.log('Guide label for:', document.querySelector('label[for="guideFile"]') ? 'FOUND' : 'NOT FOUND');
        console.log('Submission label for:', document.querySelector('label[for="submissionFile"]') ? 'FOUND' : 'NOT FOUND');

        // Test if file inputs are accessible
        if (guideFileInput) {
            console.log('Guide file input found:', guideFileInput.id);
        } else {
            console.error('Guide file input NOT FOUND');
        }

        if (submissionFileInput) {
            console.log('Submission file input found:', submissionFileInput.id);
        } else {
            console.error('Submission file input NOT FOUND');
        }

        // Add minimal fallback click handlers (only if label doesn't work)
        if (guideDropzone && guideFileInput) {
            guideDropzone.addEventListener('click', function(e) {
                // Only trigger if the label didn't handle it
                if (e.target === guideDropzone) {
                    console.log('Fallback: Guide dropzone direct click');
                    guideFileInput.click();
                }
            });
        }

        if (submissionDropzone && submissionFileInput) {
            submissionDropzone.addEventListener('click', function(e) {
                // Only trigger if the label didn't handle it
                if (e.target === submissionDropzone) {
                    console.log('Fallback: Submission dropzone direct click');
                    submissionFileInput.click();
                }
            });
        }

        // Add confirmation for buttons
        document.querySelectorAll('[data-confirm]').forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm(this.getAttribute('data-confirm'))) {
                    e.preventDefault();
                }
            });
        });

        // Add loader for processing & grading (combined functionality)
        const gradeForm = document.querySelector('form[action*="grade_submission"]');
        if (gradeForm) {
            gradeForm.addEventListener('submit', function(e) {
                showLoading('AI Processing & Grading...');
                // Let the form submit normally
            });
        }

        // Add loader for batch grading
        const batchForm = document.querySelector('form[action*="batch_grade"]');
        if (batchForm) {
            batchForm.addEventListener('submit', function(e) {
                showLoading('Batch Grading in Progress...');
                // Let the form submit normally
            });
        }





        // Add loader for clear all button
        const clearAllForm = document.getElementById('clearAllForm');
        if (clearAllForm) {
            clearAllForm.addEventListener('submit', function(e) {
                showLoading('Clearing All Data...');
                // Let the form submit normally
            });
        }

        // Global drag and drop prevention (prevent browser from opening files)
        window.addEventListener('dragover', function(e) {
            e.preventDefault();
        }, false);

        window.addEventListener('drop', function(e) {
            e.preventDefault();
        }, false);

        // Add keyboard shortcuts for upload (move variables to global scope)
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'u') {
                e.preventDefault();
                const submissionInput = document.getElementById('submissionFile');
                const guideInput = document.getElementById('guideFile');

                if (e.shiftKey) {
                    // Ctrl+Shift+U for submissions
                    submissionInput?.click();
                } else {
                    // Ctrl+U for guide
                    guideInput?.click();
                }
            }
        });

        // Note: Upload link text is now part of the clickable upload zone
        // No separate click handlers needed since the entire box is clickable

        // Add file size display helper
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    });
</script>
{% endblock %}