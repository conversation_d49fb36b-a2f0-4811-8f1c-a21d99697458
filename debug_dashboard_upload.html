<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Upload Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .debug-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .debug-log {
            background: #fff;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>Dashboard Upload Debug Tool</h1>
    <p>This tool helps debug upload functionality issues on the main dashboard.</p>
    
    <div class="debug-section">
        <h3>Quick Tests</h3>
        <button class="test-button" onclick="testDashboardConnection()">Test Dashboard Connection</button>
        <button class="test-button" onclick="testUploadElements()">Test Upload Elements</button>
        <button class="test-button" onclick="testLabelAssociation()">Test Label Association</button>
        <button class="test-button" onclick="testFileInputs()">Test File Inputs</button>
        <button class="test-button" onclick="clearLog()">Clear Log</button>
    </div>
    
    <div class="debug-section">
        <h3>Test Results</h3>
        <div id="status" class="status info">Ready to test</div>
    </div>
    
    <div class="debug-section">
        <h3>Debug Log</h3>
        <div id="log" class="debug-log"></div>
    </div>
    
    <div class="debug-section">
        <h3>Manual Instructions</h3>
        <ol>
            <li>Open the dashboard at <a href="http://127.0.0.1:8501" target="_blank">http://127.0.0.1:8501</a></li>
            <li>Open browser developer tools (F12)</li>
            <li>Go to Console tab</li>
            <li>Run: <code>testLabelFunctionality()</code></li>
            <li>Try clicking on the upload boxes</li>
            <li>Check console for any error messages</li>
        </ol>
    </div>
    
    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = `[${timestamp}] ${message}\n`;
            logElement.textContent += entry;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(message);
        }
        
        function setStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
            setStatus('Log cleared', 'info');
        }
        
        async function testDashboardConnection() {
            log('Testing dashboard connection...');
            setStatus('Testing connection...', 'info');
            
            try {
                const response = await fetch('http://127.0.0.1:8501');
                if (response.ok) {
                    log('✅ Dashboard is accessible');
                    setStatus('Dashboard connection: SUCCESS', 'success');
                    return true;
                } else {
                    log(`❌ Dashboard returned status: ${response.status}`);
                    setStatus('Dashboard connection: FAILED', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ Connection error: ${error.message}`);
                setStatus('Dashboard connection: ERROR', 'error');
                return false;
            }
        }
        
        async function testUploadElements() {
            log('Testing upload elements on dashboard...');
            setStatus('Testing upload elements...', 'info');
            
            try {
                const response = await fetch('http://127.0.0.1:8501');
                const html = await response.text();
                
                // Check for upload elements
                const hasGuideDropzone = html.includes('id="guideDropzone"');
                const hasSubmissionDropzone = html.includes('id="submissionDropzone"');
                const hasGuideInput = html.includes('id="guideFile"');
                const hasSubmissionInput = html.includes('id="submissionFile"');
                const hasLabelElements = html.includes('label for="guideFile"') && html.includes('label for="submissionFile"');
                
                log(`Guide dropzone present: ${hasGuideDropzone ? '✅' : '❌'}`);
                log(`Submission dropzone present: ${hasSubmissionDropzone ? '✅' : '❌'}`);
                log(`Guide file input present: ${hasGuideInput ? '✅' : '❌'}`);
                log(`Submission file input present: ${hasSubmissionInput ? '✅' : '❌'}`);
                log(`Label elements present: ${hasLabelElements ? '✅' : '❌'}`);
                
                if (hasGuideDropzone && hasSubmissionDropzone && hasGuideInput && hasSubmissionInput && hasLabelElements) {
                    setStatus('Upload elements: ALL PRESENT', 'success');
                    return true;
                } else {
                    setStatus('Upload elements: MISSING ELEMENTS', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ Error testing elements: ${error.message}`);
                setStatus('Upload elements: ERROR', 'error');
                return false;
            }
        }
        
        async function testLabelAssociation() {
            log('Testing label-input associations...');
            setStatus('Testing label associations...', 'info');
            
            try {
                const response = await fetch('http://127.0.0.1:8501');
                const html = await response.text();
                
                // Check label associations
                const guideLabelMatch = html.match(/label[^>]*for="guideFile"/);
                const submissionLabelMatch = html.match(/label[^>]*for="submissionFile"/);
                const guideInputMatch = html.match(/input[^>]*id="guideFile"/);
                const submissionInputMatch = html.match(/input[^>]*id="submissionFile"/);
                
                log(`Guide label-input association: ${guideLabelMatch && guideInputMatch ? '✅' : '❌'}`);
                log(`Submission label-input association: ${submissionLabelMatch && submissionInputMatch ? '✅' : '❌'}`);
                
                if (guideLabelMatch && submissionLabelMatch && guideInputMatch && submissionInputMatch) {
                    setStatus('Label associations: CORRECT', 'success');
                    return true;
                } else {
                    setStatus('Label associations: INCORRECT', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ Error testing associations: ${error.message}`);
                setStatus('Label associations: ERROR', 'error');
                return false;
            }
        }
        
        function testFileInputs() {
            log('Testing file input accessibility...');
            setStatus('Testing file inputs...', 'info');
            
            // Create test file inputs
            const testGuideInput = document.createElement('input');
            testGuideInput.type = 'file';
            testGuideInput.accept = '.docx,.txt';
            testGuideInput.style.display = 'none';
            document.body.appendChild(testGuideInput);
            
            const testSubmissionInput = document.createElement('input');
            testSubmissionInput.type = 'file';
            testSubmissionInput.accept = '.docx,.txt,.pdf,.jpg,.jpeg,.png,.tiff,.bmp,.gif';
            testSubmissionInput.multiple = true;
            testSubmissionInput.style.display = 'none';
            document.body.appendChild(testSubmissionInput);
            
            try {
                // Test clicking file inputs
                testGuideInput.click();
                log('✅ Guide file input click: SUCCESS');
                
                testSubmissionInput.click();
                log('✅ Submission file input click: SUCCESS');
                
                setStatus('File inputs: WORKING', 'success');
                
                // Clean up
                document.body.removeChild(testGuideInput);
                document.body.removeChild(testSubmissionInput);
                
                return true;
            } catch (error) {
                log(`❌ File input click error: ${error.message}`);
                setStatus('File inputs: ERROR', 'error');
                
                // Clean up
                try {
                    document.body.removeChild(testGuideInput);
                    document.body.removeChild(testSubmissionInput);
                } catch (e) {}
                
                return false;
            }
        }
        
        // Auto-run basic tests on load
        document.addEventListener('DOMContentLoaded', async function() {
            log('Debug tool loaded');
            
            const connectionOk = await testDashboardConnection();
            if (connectionOk) {
                await testUploadElements();
                await testLabelAssociation();
                testFileInputs();
            }
            
            log('Initial tests completed. Use buttons above for manual testing.');
        });
    </script>
</body>
</html>
