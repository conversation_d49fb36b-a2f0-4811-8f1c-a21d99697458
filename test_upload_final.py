#!/usr/bin/env python3
"""
Final Upload Functionality Test
Tests the upload functionality after implementing fixes
"""

import os
import sys
import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException

def test_upload_functionality():
    """Test the upload functionality using Selenium"""
    
    print("🧪 Testing Upload Functionality After Fixes")
    print("=" * 50)
    
    # Check if the app is running
    try:
        response = requests.get("http://127.0.0.1:8501", timeout=5)
        if response.status_code != 200:
            print("❌ App is not running or not accessible")
            return False
    except requests.exceptions.RequestException:
        print("❌ App is not running. Please start the app first.")
        return False
    
    print("✅ App is running and accessible")
    
    # Setup Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    driver = None
    try:
        # Initialize the driver
        driver = webdriver.Chrome(options=chrome_options)
        driver.get("http://127.0.0.1:8501")
        
        print("✅ Browser opened successfully")
        
        # Wait for page to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "guideDropzone"))
        )
        
        print("✅ Page loaded successfully")
        
        # Test 1: Check if upload elements exist
        guide_dropzone = driver.find_element(By.ID, "guideDropzone")
        guide_file_input = driver.find_element(By.ID, "guideFile")
        submission_dropzone = driver.find_element(By.ID, "submissionDropzone")
        submission_file_input = driver.find_element(By.ID, "submissionFile")
        
        print("✅ All upload elements found")
        
        # Test 2: Check if elements are visible and clickable
        assert guide_dropzone.is_displayed(), "Guide dropzone not visible"
        assert submission_dropzone.is_displayed(), "Submission dropzone not visible"
        
        print("✅ Upload zones are visible")
        
        # Test 3: Check if file inputs have correct attributes
        guide_accept = guide_file_input.get_attribute("accept")
        submission_accept = submission_file_input.get_attribute("accept")
        submission_multiple = submission_file_input.get_attribute("multiple")
        
        assert ".docx,.txt" in guide_accept, f"Guide accept attribute incorrect: {guide_accept}"
        assert ".pdf" in submission_accept, f"Submission accept attribute incorrect: {submission_accept}"
        assert submission_multiple is not None, "Submission multiple attribute missing"
        
        print("✅ File input attributes are correct")
        
        # Test 4: Test clicking on upload zones
        print("🔄 Testing click functionality...")
        
        # Execute JavaScript to test file input accessibility
        js_test_result = driver.execute_script("""
            try {
                const guideInput = document.getElementById('guideFile');
                const submissionInput = document.getElementById('submissionFile');
                
                // Test if inputs are accessible
                if (!guideInput || !submissionInput) {
                    return {success: false, error: 'File inputs not found'};
                }
                
                // Test if we can trigger click (this won't open dialog in headless mode)
                guideInput.click();
                submissionInput.click();
                
                return {success: true, message: 'File inputs are clickable'};
            } catch (error) {
                return {success: false, error: error.message};
            }
        """)
        
        if js_test_result['success']:
            print("✅ JavaScript file input click test passed")
        else:
            print(f"❌ JavaScript test failed: {js_test_result['error']}")
            return False
        
        # Test 5: Test browse buttons
        browse_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Browse Files')]")
        assert len(browse_buttons) == 2, f"Expected 2 browse buttons, found {len(browse_buttons)}"
        
        print("✅ Browse buttons found")
        
        # Test 6: Test upload link text
        upload_links = driver.find_elements(By.CLASS_NAME, "upload-link")
        assert len(upload_links) == 2, f"Expected 2 upload links, found {len(upload_links)}"
        
        print("✅ Upload link text found")
        
        # Test 7: Test CSS and styling
        guide_cursor = driver.execute_script("return getComputedStyle(arguments[0]).cursor;", guide_dropzone)
        submission_cursor = driver.execute_script("return getComputedStyle(arguments[0]).cursor;", submission_dropzone)
        
        assert guide_cursor == "pointer", f"Guide dropzone cursor should be pointer, got {guide_cursor}"
        assert submission_cursor == "pointer", f"Submission dropzone cursor should be pointer, got {submission_cursor}"
        
        print("✅ CSS cursor styling is correct")
        
        # Test 8: Test event handlers
        event_test_result = driver.execute_script("""
            // Test if event handlers are attached
            const guideDropzone = document.getElementById('guideDropzone');
            const submissionDropzone = document.getElementById('submissionDropzone');
            
            // Check if click event listeners are attached
            const guideEvents = getEventListeners ? getEventListeners(guideDropzone) : 'unknown';
            const submissionEvents = getEventListeners ? getEventListeners(submissionDropzone) : 'unknown';
            
            // Alternative test - try to trigger events
            try {
                const clickEvent = new Event('click', {bubbles: true});
                guideDropzone.dispatchEvent(clickEvent);
                submissionDropzone.dispatchEvent(clickEvent);
                return {success: true, message: 'Events can be dispatched'};
            } catch (error) {
                return {success: false, error: error.message};
            }
        """)
        
        if event_test_result['success']:
            print("✅ Event handling test passed")
        else:
            print(f"⚠️  Event handling test warning: {event_test_result['error']}")
        
        # Test 9: Console error check
        logs = driver.get_log('browser')
        errors = [log for log in logs if log['level'] == 'SEVERE']
        
        if errors:
            print("⚠️  Browser console errors found:")
            for error in errors:
                print(f"   - {error['message']}")
        else:
            print("✅ No browser console errors")
        
        print("\n🎉 All upload functionality tests passed!")
        print("\n📋 Test Summary:")
        print("   ✅ Upload elements exist and are visible")
        print("   ✅ File input attributes are correct")
        print("   ✅ JavaScript functionality works")
        print("   ✅ Browse buttons are present")
        print("   ✅ Upload links are present")
        print("   ✅ CSS styling is correct")
        print("   ✅ Event handling works")
        
        return True
        
    except TimeoutException:
        print("❌ Timeout waiting for page elements")
        return False
    except WebDriverException as e:
        print(f"❌ WebDriver error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    finally:
        if driver:
            driver.quit()

def test_manual_instructions():
    """Provide manual testing instructions"""
    print("\n🔧 Manual Testing Instructions:")
    print("=" * 50)
    print("1. Open http://127.0.0.1:8501 in your browser")
    print("2. Open browser developer tools (F12)")
    print("3. Go to Console tab")
    print("4. Run: debugUploadElements()")
    print("5. Try clicking on the upload zones")
    print("6. Try clicking on the 'Browse Files' buttons")
    print("7. Try clicking on the 'click to browse' text")
    print("8. Check console for any error messages")
    print("\n💡 Expected behavior:")
    print("   - Clicking should open file selection dialog")
    print("   - Console should show click events")
    print("   - No JavaScript errors should appear")

if __name__ == "__main__":
    try:
        success = test_upload_functionality()
        if not success:
            print("\n❌ Automated tests failed. Trying manual testing instructions...")
            test_manual_instructions()
            sys.exit(1)
        else:
            print("\n✅ All tests passed! Upload functionality should be working.")
            test_manual_instructions()
            sys.exit(0)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test script error: {e}")
        test_manual_instructions()
        sys.exit(1)
