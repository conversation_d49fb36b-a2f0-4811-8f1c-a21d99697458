<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .iframe-container {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
            height: 600px;
        }
        
        .dashboard-frame {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .instructions {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 Upload Functionality Test</h1>
    <p>This tool tests the file upload functionality on the dashboard.</p>
    
    <div class="test-container">
        <h3>📊 Test Status</h3>
        <div id="testStatus" class="test-result info">
            Initializing tests...
        </div>
        
        <div>
            <button class="test-button" onclick="testDashboardAccess()">Test Dashboard Access</button>
            <button class="test-button" onclick="testUploadElements()">Test Upload Elements</button>
            <button class="test-button" onclick="testFileInputs()">Test File Inputs</button>
            <button class="test-button" onclick="clearLog()">Clear Log</button>
        </div>
    </div>
    
    <div class="test-container">
        <h3>📝 Test Log</h3>
        <div id="testLog" class="log">
            <div style="color: #6c757d;">Test log initialized. Click buttons above to run tests.</div>
        </div>
    </div>
    
    <div class="test-container">
        <h3>🖥️ Dashboard Test</h3>
        <div class="instructions">
            <strong>Manual Test Instructions:</strong>
            <ol>
                <li>The dashboard is loaded in the frame below</li>
                <li>Try clicking on the upload boxes</li>
                <li>Check if file dialogs open</li>
                <li>Open browser console (F12) to see debug messages</li>
                <li>Look for any JavaScript errors in the console</li>
            </ol>
        </div>
        
        <div class="iframe-container">
            <iframe id="dashboardFrame" class="dashboard-frame" src="http://127.0.0.1:8501"></iframe>
        </div>
    </div>
    
    <script>
        let testResults = [];
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.style.marginBottom = '5px';
            
            if (type === 'error') {
                entry.style.color = '#dc3545';
                entry.innerHTML = `<strong>[${timestamp}] ERROR:</strong> ${message}`;
            } else if (type === 'success') {
                entry.style.color = '#28a745';
                entry.innerHTML = `<strong>[${timestamp}] SUCCESS:</strong> ${message}`;
            } else if (type === 'warning') {
                entry.style.color = '#ffc107';
                entry.innerHTML = `<strong>[${timestamp}] WARNING:</strong> ${message}`;
            } else {
                entry.style.color = '#17a2b8';
                entry.innerHTML = `<strong>[${timestamp}] INFO:</strong> ${message}`;
            }
            
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('testStatus');
            statusElement.textContent = message;
            statusElement.className = `test-result ${type}`;
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '<div style="color: #6c757d;">Test log cleared.</div>';
            testResults = [];
            updateStatus('Ready to run tests', 'info');
        }
        
        async function testDashboardAccess() {
            log('Testing dashboard access...', 'info');
            updateStatus('Testing dashboard access...', 'info');
            
            try {
                const response = await fetch('http://127.0.0.1:8501');
                if (response.ok) {
                    log('Dashboard is accessible', 'success');
                    testResults.push({test: 'dashboard_access', result: 'pass'});
                    updateStatus('Dashboard access: SUCCESS', 'success');
                    return true;
                } else {
                    log(`Dashboard returned status: ${response.status}`, 'error');
                    testResults.push({test: 'dashboard_access', result: 'fail'});
                    updateStatus('Dashboard access: FAILED', 'error');
                    return false;
                }
            } catch (error) {
                log(`Connection error: ${error.message}`, 'error');
                testResults.push({test: 'dashboard_access', result: 'fail'});
                updateStatus('Dashboard access: ERROR', 'error');
                return false;
            }
        }
        
        async function testUploadElements() {
            log('Testing upload elements...', 'info');
            updateStatus('Testing upload elements...', 'info');
            
            try {
                const response = await fetch('http://127.0.0.1:8501');
                const html = await response.text();
                
                // Check for required elements
                const checks = [
                    {name: 'Guide dropzone', pattern: 'id="guideDropzone"'},
                    {name: 'Submission dropzone', pattern: 'id="submissionDropzone"'},
                    {name: 'Guide file input', pattern: 'id="guideFile"'},
                    {name: 'Submission file input', pattern: 'id="submissionFile"'},
                    {name: 'Guide form', pattern: 'id="guideForm"'},
                    {name: 'Submission form', pattern: 'id="submissionForm"'},
                    {name: 'Guide label', pattern: 'label for="guideFile"'},
                    {name: 'Submission label', pattern: 'label for="submissionFile"'},
                ];
                
                let allFound = true;
                for (const check of checks) {
                    if (html.includes(check.pattern)) {
                        log(`✓ ${check.name} found`, 'success');
                    } else {
                        log(`✗ ${check.name} missing`, 'error');
                        allFound = false;
                    }
                }
                
                if (allFound) {
                    log('All upload elements found', 'success');
                    testResults.push({test: 'upload_elements', result: 'pass'});
                    updateStatus('Upload elements: ALL FOUND', 'success');
                    return true;
                } else {
                    log('Some upload elements missing', 'error');
                    testResults.push({test: 'upload_elements', result: 'fail'});
                    updateStatus('Upload elements: MISSING ELEMENTS', 'error');
                    return false;
                }
            } catch (error) {
                log(`Error testing elements: ${error.message}`, 'error');
                testResults.push({test: 'upload_elements', result: 'fail'});
                updateStatus('Upload elements: ERROR', 'error');
                return false;
            }
        }
        
        function testFileInputs() {
            log('Testing file input functionality...', 'info');
            updateStatus('Testing file inputs...', 'info');
            
            try {
                // Create test file inputs
                const testGuideInput = document.createElement('input');
                testGuideInput.type = 'file';
                testGuideInput.accept = '.docx,.txt';
                testGuideInput.style.display = 'none';
                document.body.appendChild(testGuideInput);
                
                const testSubmissionInput = document.createElement('input');
                testSubmissionInput.type = 'file';
                testSubmissionInput.accept = '.docx,.txt,.pdf,.jpg,.jpeg,.png,.tiff,.bmp,.gif';
                testSubmissionInput.multiple = true;
                testSubmissionInput.style.display = 'none';
                document.body.appendChild(testSubmissionInput);
                
                // Test clicking file inputs
                log('Testing guide file input click...', 'info');
                testGuideInput.click();
                log('Guide file input click: SUCCESS', 'success');
                
                log('Testing submission file input click...', 'info');
                testSubmissionInput.click();
                log('Submission file input click: SUCCESS', 'success');
                
                // Clean up
                document.body.removeChild(testGuideInput);
                document.body.removeChild(testSubmissionInput);
                
                log('File input functionality test completed', 'success');
                testResults.push({test: 'file_inputs', result: 'pass'});
                updateStatus('File inputs: WORKING', 'success');
                return true;
                
            } catch (error) {
                log(`File input test error: ${error.message}`, 'error');
                testResults.push({test: 'file_inputs', result: 'fail'});
                updateStatus('File inputs: ERROR', 'error');
                return false;
            }
        }
        
        // Auto-run tests on load
        document.addEventListener('DOMContentLoaded', async function() {
            log('Upload functionality test initialized', 'info');
            
            // Run basic tests
            const dashboardOk = await testDashboardAccess();
            if (dashboardOk) {
                await testUploadElements();
                testFileInputs();
            }
            
            // Summary
            const passedTests = testResults.filter(t => t.result === 'pass').length;
            const totalTests = testResults.length;
            
            if (passedTests === totalTests && totalTests > 0) {
                log(`All tests passed (${passedTests}/${totalTests})`, 'success');
                updateStatus(`All tests passed (${passedTests}/${totalTests})`, 'success');
            } else {
                log(`Tests completed: ${passedTests}/${totalTests} passed`, 'warning');
                updateStatus(`Tests: ${passedTests}/${totalTests} passed`, 'warning');
            }
            
            log('Manual testing: Try clicking on upload boxes in the dashboard below', 'info');
        });
        
        // Monitor iframe for errors
        document.getElementById('dashboardFrame').addEventListener('load', function() {
            log('Dashboard iframe loaded successfully', 'success');
            
            try {
                const iframeWindow = this.contentWindow;
                
                // Try to access iframe content (may fail due to cross-origin)
                iframeWindow.addEventListener('error', function(e) {
                    log(`Dashboard error: ${e.message}`, 'error');
                });
                
                log('Dashboard error monitoring enabled', 'info');
            } catch (e) {
                log('Cannot monitor iframe errors (cross-origin restrictions)', 'warning');
            }
        });
        
        document.getElementById('dashboardFrame').addEventListener('error', function() {
            log('Failed to load dashboard iframe', 'error');
            updateStatus('Dashboard iframe: FAILED TO LOAD', 'error');
        });
    </script>
</body>
</html>
