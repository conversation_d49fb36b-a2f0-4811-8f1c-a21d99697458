#!/usr/bin/env python3
"""
Test web upload functionality to verify it's working
"""

import requests
import tempfile
import os
import time

def test_web_upload():
    base_url = "http://127.0.0.1:8501"
    
    print("🧪 Testing Web Upload Functionality")
    print("=" * 50)
    
    # Test server connectivity
    try:
        response = requests.get(base_url, timeout=10)
        if response.status_code == 200:
            print("✅ Server is accessible")
        else:
            print(f"❌ Server returned status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Server not accessible: {e}")
        return False
    
    # Create test guide file
    guide_content = """
    MARKING GUIDE - Web Upload Test
    
    Question 1 (10 marks): What is web development?
    Expected Answer: Web development involves creating websites and web applications.
    
    Question 2 (15 marks): Explain JavaScript.
    Expected Answer: JavaScript is a programming language used for web development.
    """
    
    # Create test submission file
    submission_content = """
    STUDENT SUBMISSION - Web Upload Test
    
    Answer 1: Web development is the process of building websites.
    
    Answer 2: JavaScript is a scripting language for web pages.
    """
    
    try:
        # Test guide upload
        print("\n📄 Testing Guide Upload...")
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(guide_content)
            guide_path = f.name
        
        with open(guide_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{base_url}/upload_guide", files=files, timeout=30)
            
        if response.status_code in [200, 302]:
            print("✅ Guide upload successful")
            guide_success = True
        else:
            print(f"❌ Guide upload failed: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            guide_success = False
        
        # Test submission upload
        print("\n📁 Testing Submission Upload...")
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(submission_content)
            submission_path = f.name
        
        with open(submission_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{base_url}/upload_submission", files=files, timeout=30)
            
        if response.status_code in [200, 302]:
            print("✅ Submission upload successful")
            submission_success = True
        else:
            print(f"❌ Submission upload failed: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            submission_success = False
        
        # Test dashboard access
        print("\n🏠 Testing Dashboard Access...")
        response = requests.get(base_url, timeout=10)
        if response.status_code == 200:
            print("✅ Dashboard accessible after uploads")
            dashboard_success = True
        else:
            print(f"❌ Dashboard failed: {response.status_code}")
            dashboard_success = False
        
        # Clean up
        try:
            os.unlink(guide_path)
            os.unlink(submission_path)
            print("✅ Test files cleaned up")
        except:
            pass
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        results = {
            "Guide Upload": guide_success,
            "Submission Upload": submission_success,
            "Dashboard Access": dashboard_success
        }
        
        passed = 0
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name:<20} {status}")
            if result:
                passed += 1
        
        print("-" * 50)
        print(f"TOTAL: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("\n🎉 ALL UPLOAD TESTS PASSED!")
            print("The upload functionality is working correctly.")
            print("If the web interface isn't working, it's likely a JavaScript issue.")
            return True
        else:
            print("\n⚠️ Some upload tests failed.")
            return False
            
    except Exception as e:
        print(f"❌ Upload test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = test_web_upload()
    exit(0 if success else 1)
