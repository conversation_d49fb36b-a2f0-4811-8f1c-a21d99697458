<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .upload-zone {
            border: 2px dashed #ccc;
            padding: 40px;
            text-align: center;
            background: white;
            margin: 20px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-zone:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }
        .upload-zone.dragover {
            border-color: #007bff;
            background: #e3f2fd;
            transform: scale(1.02);
        }
        .debug-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-info { color: #007bff; }
        .log-warning { color: #ffc107; }
        .clear-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>Upload Functionality Debug Test</h1>
    <p>Testing upload functionality for: <strong>http://127.0.0.1:8501</strong></p>
    
    <div class="debug-log">
        <button class="clear-btn" onclick="clearLog()">Clear Log</button>
        <div id="log-content"></div>
    </div>

    <!-- Guide Upload Test -->
    <h3>Guide Upload Test</h3>
    <form action="http://127.0.0.1:8501/upload_guide" method="post" enctype="multipart/form-data" id="guideForm">
        <div class="upload-zone" id="guideDropzone">
            <input type="file" id="guideFile" name="file" accept=".docx,.txt" style="display: none;">
            <h4>📄 Drop Guide Here or Click to Browse</h4>
            <p>Supports: .docx, .txt files</p>
            <small>Max size: 20MB</small>
        </div>
    </form>

    <!-- Submission Upload Test -->
    <h3>Submission Upload Test</h3>
    <form action="http://127.0.0.1:8501/upload_submission" method="post" enctype="multipart/form-data" id="submissionForm">
        <div class="upload-zone" id="submissionDropzone">
            <input type="file" id="submissionFile" name="file" accept=".docx,.txt,.pdf,.jpg,.jpeg,.png,.tiff,.bmp,.gif" multiple style="display: none;">
            <h4>📁 Drop Submissions Here or Click to Browse</h4>
            <p>Supports: PDF, Word, Text, Images</p>
            <small>Multiple files allowed • Max size: 20MB each</small>
        </div>
    </form>

    <script>
        function log(message, type = 'info') {
            const logContent = document.getElementById('log-content');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logContent.appendChild(entry);
            logContent.scrollTop = logContent.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log-content').innerHTML = '';
        }

        // Validation function
        function validateFile(file, allowedTypes, maxSize = 20 * 1024 * 1024) {
            const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
            const isValidType = allowedTypes.includes(fileExtension);
            const isValidSize = file.size <= maxSize;
            
            return {
                valid: isValidType && isValidSize,
                error: !isValidType ? `File type ${fileExtension} not supported` : 
                       !isValidSize ? `File size too large (max 20MB)` : null
            };
        }

        // Format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        document.addEventListener('DOMContentLoaded', function() {
            log('DOM Content Loaded - Starting upload debug test', 'success');

            // Guide upload setup
            const guideDropzone = document.getElementById('guideDropzone');
            const guideFileInput = document.getElementById('guideFile');
            const guideForm = document.getElementById('guideForm');
            
            if (guideDropzone && guideFileInput && guideForm) {
                log('Guide upload elements found', 'success');
                const allowedGuideTypes = ['.docx', '.txt'];
                
                // Click handler
                guideDropzone.addEventListener('click', function() {
                    log('Guide dropzone clicked', 'info');
                    guideFileInput.click();
                });
                
                // File input change
                guideFileInput.addEventListener('change', function() {
                    log(`Guide file input changed, files: ${this.files.length}`, 'info');
                    if (this.files.length > 0) {
                        const file = this.files[0];
                        log(`Selected file: ${file.name} (${formatFileSize(file.size)})`, 'info');
                        
                        const validation = validateFile(file, allowedGuideTypes);
                        if (validation.valid) {
                            log('File validation passed - submitting form', 'success');
                            guideForm.submit();
                        } else {
                            log(`File validation failed: ${validation.error}`, 'error');
                            this.value = '';
                        }
                    }
                });
                
                // Drag and drop
                function preventDefaults(e) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                
                ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                    guideDropzone.addEventListener(eventName, preventDefaults, false);
                });
                
                ['dragenter', 'dragover'].forEach(eventName => {
                    guideDropzone.addEventListener(eventName, function() {
                        guideDropzone.classList.add('dragover');
                        log(`Guide drag event: ${eventName}`, 'info');
                    }, false);
                });
                
                ['dragleave', 'drop'].forEach(eventName => {
                    guideDropzone.addEventListener(eventName, function() {
                        guideDropzone.classList.remove('dragover');
                    }, false);
                });
                
                guideDropzone.addEventListener('drop', function(e) {
                    const files = e.dataTransfer.files;
                    log(`Guide drop event - files: ${files.length}`, 'info');
                    
                    if (files.length > 0) {
                        const file = files[0];
                        log(`Dropped file: ${file.name} (${formatFileSize(file.size)})`, 'info');
                        
                        const validation = validateFile(file, allowedGuideTypes);
                        if (validation.valid) {
                            guideFileInput.files = files;
                            log('Drop validation passed - submitting form', 'success');
                            guideForm.submit();
                        } else {
                            log(`Drop validation failed: ${validation.error}`, 'error');
                        }
                    }
                });
                
            } else {
                log('Guide upload elements NOT found!', 'error');
            }

            // Submission upload setup (similar to guide)
            const submissionDropzone = document.getElementById('submissionDropzone');
            const submissionFileInput = document.getElementById('submissionFile');
            const submissionForm = document.getElementById('submissionForm');
            
            if (submissionDropzone && submissionFileInput && submissionForm) {
                log('Submission upload elements found', 'success');
                const allowedSubmissionTypes = ['.docx', '.txt', '.pdf', '.jpg', '.jpeg', '.png', '.tiff', '.bmp', '.gif'];
                
                submissionDropzone.addEventListener('click', function() {
                    log('Submission dropzone clicked', 'info');
                    submissionFileInput.click();
                });
                
                submissionFileInput.addEventListener('change', function() {
                    log(`Submission file input changed, files: ${this.files.length}`, 'info');
                    if (this.files.length > 0) {
                        const files = Array.from(this.files);
                        let allValid = true;
                        let errorMessage = '';
                        
                        for (let file of files) {
                            const validation = validateFile(file, allowedSubmissionTypes);
                            if (!validation.valid) {
                                allValid = false;
                                errorMessage = validation.error;
                                break;
                            }
                        }
                        
                        if (allValid) {
                            log(`All ${files.length} files valid - submitting form`, 'success');
                            submissionForm.submit();
                        } else {
                            log(`File validation failed: ${errorMessage}`, 'error');
                            this.value = '';
                        }
                    }
                });
                
            } else {
                log('Submission upload elements NOT found!', 'error');
            }
            
            log('Upload debug setup complete', 'success');
        });
    </script>
</body>
</html>
