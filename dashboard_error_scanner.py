#!/usr/bin/env python3
"""
Comprehensive Dashboard Error Scanner
Scans the dashboard for all types of errors including HTML, CSS, JavaScript, and functionality issues.
"""

import requests
import re
import json
import sys
from pathlib import Path
from urllib.parse import urljoin, urlparse
import time

class DashboardErrorScanner:
    def __init__(self, base_url="http://127.0.0.1:8501"):
        self.base_url = base_url
        self.errors = []
        self.warnings = []
        self.info = []
        
    def log_error(self, category, message, details=None):
        """Log an error with category and details"""
        error = {
            'category': category,
            'message': message,
            'details': details,
            'severity': 'error'
        }
        self.errors.append(error)
        print(f"❌ ERROR [{category}]: {message}")
        if details:
            print(f"   Details: {details}")
    
    def log_warning(self, category, message, details=None):
        """Log a warning with category and details"""
        warning = {
            'category': category,
            'message': message,
            'details': details,
            'severity': 'warning'
        }
        self.warnings.append(warning)
        print(f"⚠️  WARNING [{category}]: {message}")
        if details:
            print(f"   Details: {details}")
    
    def log_info(self, category, message, details=None):
        """Log info with category and details"""
        info = {
            'category': category,
            'message': message,
            'details': details,
            'severity': 'info'
        }
        self.info.append(info)
        print(f"ℹ️  INFO [{category}]: {message}")
    
    def test_connection(self):
        """Test if the dashboard is accessible"""
        print("🔍 Testing Dashboard Connection...")
        try:
            response = requests.get(self.base_url, timeout=10)
            if response.status_code == 200:
                self.log_info("CONNECTION", "Dashboard is accessible")
                return response.text
            else:
                self.log_error("CONNECTION", f"Dashboard returned status {response.status_code}")
                return None
        except requests.exceptions.RequestException as e:
            self.log_error("CONNECTION", f"Cannot connect to dashboard: {e}")
            return None
    
    def scan_html_structure(self, html_content):
        """Scan HTML for structural issues"""
        print("🔍 Scanning HTML Structure...")
        
        # Check for required elements
        required_elements = [
            ('id="guideDropzone"', 'Guide upload dropzone'),
            ('id="submissionDropzone"', 'Submission upload dropzone'),
            ('id="guideFile"', 'Guide file input'),
            ('id="submissionFile"', 'Submission file input'),
            ('id="guideForm"', 'Guide upload form'),
            ('id="submissionForm"', 'Submission upload form'),
        ]
        
        for element, description in required_elements:
            if element not in html_content:
                self.log_error("HTML_STRUCTURE", f"Missing element: {description}", element)
            else:
                self.log_info("HTML_STRUCTURE", f"Found element: {description}")
        
        # Check for label associations
        guide_label_match = re.search(r'<label[^>]*for="guideFile"', html_content)
        submission_label_match = re.search(r'<label[^>]*for="submissionFile"', html_content)
        
        if not guide_label_match:
            self.log_error("HTML_STRUCTURE", "Missing label for guide file input")
        if not submission_label_match:
            self.log_error("HTML_STRUCTURE", "Missing label for submission file input")
        
        # Check for duplicate IDs
        id_pattern = r'id="([^"]+)"'
        ids = re.findall(id_pattern, html_content)
        duplicate_ids = [id for id in set(ids) if ids.count(id) > 1]
        
        for dup_id in duplicate_ids:
            self.log_error("HTML_STRUCTURE", f"Duplicate ID found: {dup_id}")
        
        # Check for unclosed tags
        unclosed_patterns = [
            (r'<div[^>]*>(?!.*</div>)', 'Unclosed div tags'),
            (r'<form[^>]*>(?!.*</form>)', 'Unclosed form tags'),
            (r'<script[^>]*>(?!.*</script>)', 'Unclosed script tags'),
        ]
        
        for pattern, description in unclosed_patterns:
            matches = re.findall(pattern, html_content, re.DOTALL)
            if matches:
                self.log_warning("HTML_STRUCTURE", description, f"Found {len(matches)} instances")
    
    def scan_css_issues(self, html_content):
        """Scan for CSS-related issues"""
        print("🔍 Scanning CSS Issues...")
        
        # Check for CSS file inclusion
        if 'style.css' not in html_content:
            self.log_error("CSS", "Main CSS file not included")
        
        # Check for inline styles that might conflict
        inline_style_pattern = r'style="[^"]*pointer-events[^"]*"'
        inline_styles = re.findall(inline_style_pattern, html_content)
        
        if inline_styles:
            self.log_warning("CSS", f"Found {len(inline_styles)} inline pointer-events styles")
        
        # Check for CSS class usage
        required_classes = [
            'upload-dropzone',
            'upload-content',
            'upload-icon',
            'upload-text',
            'main-card',
            'dashboard-grid'
        ]
        
        for css_class in required_classes:
            if f'class="{css_class}"' not in html_content and f'class="[^"]*{css_class}[^"]*"' not in html_content:
                self.log_warning("CSS", f"CSS class not found in HTML: {css_class}")
    
    def scan_javascript_issues(self, html_content):
        """Scan for JavaScript-related issues"""
        print("🔍 Scanning JavaScript Issues...")
        
        # Check for JavaScript inclusion
        if '<script>' not in html_content and '<script ' not in html_content:
            self.log_error("JAVASCRIPT", "No JavaScript found in page")
        
        # Check for common JavaScript errors
        js_error_patterns = [
            (r'console\.error\([^)]+\)', 'Console error statements found'),
            (r'throw new Error\([^)]+\)', 'Error throwing statements found'),
            (r'undefined\s*\.\s*\w+', 'Potential undefined property access'),
        ]
        
        for pattern, description in js_error_patterns:
            matches = re.findall(pattern, html_content)
            if matches:
                self.log_warning("JAVASCRIPT", description, f"Found {len(matches)} instances")
        
        # Check for event handlers
        event_handlers = [
            'addEventListener',
            'onclick',
            'onchange',
            'ondrop',
            'ondragover'
        ]
        
        for handler in event_handlers:
            if handler in html_content:
                self.log_info("JAVASCRIPT", f"Found event handler: {handler}")
        
        # Check for required functions
        required_functions = [
            'testLabelFunctionality',
            'debugUploadElements',
            'preventDefaults'
        ]
        
        for func in required_functions:
            if func not in html_content:
                self.log_warning("JAVASCRIPT", f"Function not found: {func}")
    
    def scan_form_issues(self, html_content):
        """Scan for form-related issues"""
        print("🔍 Scanning Form Issues...")
        
        # Check form attributes
        form_patterns = [
            (r'<form[^>]*action="[^"]*upload_guide[^"]*"', 'Guide upload form action'),
            (r'<form[^>]*action="[^"]*upload_submission[^"]*"', 'Submission upload form action'),
            (r'<form[^>]*method="post"', 'POST method forms'),
            (r'<form[^>]*enctype="multipart/form-data"', 'Multipart form encoding'),
        ]
        
        for pattern, description in form_patterns:
            if not re.search(pattern, html_content):
                self.log_error("FORMS", f"Missing or incorrect: {description}")
            else:
                self.log_info("FORMS", f"Found: {description}")
        
        # Check file input attributes
        file_input_patterns = [
            (r'<input[^>]*type="file"[^>]*accept="[^"]*\.docx[^"]*"', 'Guide file input with docx accept'),
            (r'<input[^>]*type="file"[^>]*accept="[^"]*\.pdf[^"]*"', 'Submission file input with pdf accept'),
            (r'<input[^>]*type="file"[^>]*multiple', 'Multiple file input for submissions'),
        ]
        
        for pattern, description in file_input_patterns:
            if not re.search(pattern, html_content):
                self.log_warning("FORMS", f"Missing or incorrect: {description}")
    
    def scan_accessibility_issues(self, html_content):
        """Scan for accessibility issues"""
        print("🔍 Scanning Accessibility Issues...")
        
        # Check for alt attributes on images
        img_without_alt = re.findall(r'<img[^>]*(?!.*alt=)[^>]*>', html_content)
        if img_without_alt:
            self.log_warning("ACCESSIBILITY", f"Found {len(img_without_alt)} images without alt attributes")
        
        # Check for form labels
        inputs_without_labels = []
        input_pattern = r'<input[^>]*id="([^"]+)"[^>]*>'
        inputs = re.findall(input_pattern, html_content)
        
        for input_id in inputs:
            label_pattern = f'<label[^>]*for="{input_id}"[^>]*>'
            if not re.search(label_pattern, html_content):
                inputs_without_labels.append(input_id)
        
        if inputs_without_labels:
            self.log_warning("ACCESSIBILITY", f"Inputs without labels: {inputs_without_labels}")
        
        # Check for heading structure
        headings = re.findall(r'<h([1-6])[^>]*>', html_content)
        if headings:
            heading_levels = [int(h) for h in headings]
            if heading_levels and min(heading_levels) > 1:
                self.log_warning("ACCESSIBILITY", "Page doesn't start with h1")
    
    def scan_performance_issues(self, html_content):
        """Scan for performance issues"""
        print("🔍 Scanning Performance Issues...")
        
        # Check for large inline scripts
        script_pattern = r'<script[^>]*>(.*?)</script>'
        scripts = re.findall(script_pattern, html_content, re.DOTALL)
        
        for i, script in enumerate(scripts):
            if len(script) > 10000:  # 10KB
                self.log_warning("PERFORMANCE", f"Large inline script found (Script {i+1}): {len(script)} characters")
        
        # Check for external resource loading
        external_resources = [
            (r'<link[^>]*href="https?://[^"]*"', 'External CSS'),
            (r'<script[^>]*src="https?://[^"]*"', 'External JavaScript'),
        ]
        
        for pattern, resource_type in external_resources:
            matches = re.findall(pattern, html_content)
            if matches:
                self.log_info("PERFORMANCE", f"Found {len(matches)} {resource_type} resources")
    
    def generate_report(self):
        """Generate a comprehensive error report"""
        print("\n" + "="*80)
        print("📊 DASHBOARD ERROR SCAN REPORT")
        print("="*80)
        
        print(f"\n📈 SUMMARY:")
        print(f"   ❌ Errors: {len(self.errors)}")
        print(f"   ⚠️  Warnings: {len(self.warnings)}")
        print(f"   ℹ️  Info: {len(self.info)}")
        
        if self.errors:
            print(f"\n❌ ERRORS ({len(self.errors)}):")
            for error in self.errors:
                print(f"   • [{error['category']}] {error['message']}")
                if error['details']:
                    print(f"     Details: {error['details']}")
        
        if self.warnings:
            print(f"\n⚠️  WARNINGS ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"   • [{warning['category']}] {warning['message']}")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        if len(self.errors) == 0:
            print("   ✅ No critical errors found!")
        else:
            print("   🔧 Fix critical errors first")
            print("   🧪 Test upload functionality after fixes")
        
        if len(self.warnings) > 0:
            print("   ⚡ Address warnings for better performance")
        
        print("   🔍 Use browser developer tools for runtime errors")
        print("   📱 Test on different browsers and devices")
        
        return len(self.errors) == 0
    
    def run_full_scan(self):
        """Run complete dashboard error scan"""
        print("🚀 Starting Comprehensive Dashboard Error Scan...")
        print("="*80)
        
        # Test connection and get HTML
        html_content = self.test_connection()
        if not html_content:
            return False
        
        # Run all scans
        self.scan_html_structure(html_content)
        self.scan_css_issues(html_content)
        self.scan_javascript_issues(html_content)
        self.scan_form_issues(html_content)
        self.scan_accessibility_issues(html_content)
        self.scan_performance_issues(html_content)
        
        # Generate report
        return self.generate_report()

def main():
    """Main function to run the dashboard error scanner"""
    scanner = DashboardErrorScanner()
    success = scanner.run_full_scan()
    
    # Save detailed report
    report_data = {
        'errors': scanner.errors,
        'warnings': scanner.warnings,
        'info': scanner.info,
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'success': success
    }
    
    with open('dashboard_error_report.json', 'w') as f:
        json.dump(report_data, f, indent=2)
    
    print(f"\n📄 Detailed report saved to: dashboard_error_report.json")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
