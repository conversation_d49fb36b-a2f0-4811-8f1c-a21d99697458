#!/usr/bin/env python3
"""
Basic Functionality Test

This script tests the core functionality without requiring API keys or external dependencies.
"""

import os
import sys
import tempfile
from pathlib import Path

# Add the src and webapp directories to the Python path
project_root = Path(__file__).parent
src_path = project_root / "src"
webapp_path = project_root / "webapp"
sys.path.insert(0, str(src_path))
sys.path.insert(0, str(webapp_path))

def test_directory_structure():
    """Test that required directories exist."""
    print("🧪 Testing Directory Structure...")
    
    required_dirs = [
        "src",
        "webapp",
        "webapp/templates",
        "webapp/static",
        "webapp/static/css"
    ]
    
    all_exist = True
    for dir_path in required_dirs:
        full_path = project_root / dir_path
        if full_path.exists():
            print(f"✅ {dir_path} exists")
        else:
            print(f"❌ {dir_path} missing")
            all_exist = False
    
    return all_exist

def test_template_files():
    """Test that required template files exist."""
    print("\n🧪 Testing Template Files...")
    
    required_templates = [
        "webapp/templates/base.html",
        "webapp/templates/index.html",
        "webapp/templates/settings.html",
        "webapp/templates/help.html",
        "webapp/templates/batch_results.html"
    ]
    
    all_exist = True
    for template_path in required_templates:
        full_path = project_root / template_path
        if full_path.exists():
            print(f"✅ {template_path} exists")
        else:
            print(f"❌ {template_path} missing")
            all_exist = False
    
    return all_exist

def test_config_import():
    """Test that configuration can be imported."""
    print("\n🧪 Testing Configuration Import...")
    
    try:
        from src.config.config_manager import ConfigManager
        config_manager = ConfigManager()
        config = config_manager.config
        print("✅ Configuration imported successfully")
        print(f"   Temp dir: {config.temp_dir}")
        print(f"   Max file size: {config.max_file_size_mb}MB")
        return True
    except Exception as e:
        print(f"❌ Configuration import failed: {e}")
        return False

def test_upload_directory():
    """Test upload directory creation."""
    print("\n🧪 Testing Upload Directory...")
    
    try:
        from src.config.config_manager import ConfigManager
        config_manager = ConfigManager()
        config = config_manager.config
        
        upload_dir = os.path.join(config.temp_dir, 'uploads')
        
        # Create directory if it doesn't exist
        os.makedirs(upload_dir, exist_ok=True)
        
        if os.path.exists(upload_dir):
            print(f"✅ Upload directory exists: {upload_dir}")
            
            # Test write permissions
            test_file = os.path.join(upload_dir, 'test_write.txt')
            try:
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)
                print("✅ Upload directory is writable")
                return True
            except Exception as e:
                print(f"❌ Upload directory not writable: {e}")
                return False
        else:
            print("❌ Upload directory could not be created")
            return False
            
    except Exception as e:
        print(f"❌ Upload directory test failed: {e}")
        return False

def test_flask_app():
    """Test Flask app creation."""
    print("\n🧪 Testing Flask App...")
    
    try:
        from webapp.app import create_app
        app = create_app()
        print("✅ Flask app created successfully")
        
        # Test configuration
        if 'UPLOAD_FOLDER' in app.config:
            print(f"✅ Upload folder configured: {app.config['UPLOAD_FOLDER']}")
        else:
            print("❌ Upload folder not configured")
            return False
            
        if 'SECRET_KEY' in app.config:
            print("✅ Secret key configured")
        else:
            print("❌ Secret key not configured")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Flask app creation failed: {e}")
        return False

def test_routes():
    """Test that routes are accessible."""
    print("\n🧪 Testing Routes...")
    
    try:
        from webapp.app import create_app
        app = create_app()
        
        with app.test_client() as client:
            routes_to_test = [
                ('/', 'Dashboard'),
                ('/settings', 'Settings'),
                ('/help', 'Help')
            ]
            
            for route, name in routes_to_test:
                try:
                    response = client.get(route)
                    if response.status_code == 200:
                        print(f"✅ {name} route ({route}) accessible")
                    else:
                        print(f"❌ {name} route ({route}) returns {response.status_code}")
                        return False
                except Exception as e:
                    print(f"❌ Error accessing {name} route: {e}")
                    return False
            
            return True
            
    except Exception as e:
        print(f"❌ Route testing failed: {e}")
        return False

def test_css_files():
    """Test that CSS files exist and are readable."""
    print("\n🧪 Testing CSS Files...")
    
    css_files = [
        "webapp/static/css/style.css"
    ]
    
    for css_file in css_files:
        full_path = project_root / css_file
        if full_path.exists():
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if len(content) > 1000:  # Basic check for substantial content
                        print(f"✅ {css_file} exists and has content ({len(content)} chars)")
                    else:
                        print(f"⚠️  {css_file} exists but seems small ({len(content)} chars)")
            except Exception as e:
                print(f"❌ Error reading {css_file}: {e}")
                return False
        else:
            print(f"❌ {css_file} missing")
            return False
    
    return True

def test_performance_config():
    """Test performance configuration."""
    print("\n🧪 Testing Performance Configuration...")
    
    try:
        from src.config.llm_performance import get_profile, get_task_settings
        
        # Test profiles
        profiles = ["speed", "balanced", "accuracy"]
        for profile_name in profiles:
            profile = get_profile(profile_name)
            if isinstance(profile, dict) and 'model' in profile:
                print(f"✅ {profile_name} profile loaded: {profile['model']}")
            else:
                print(f"❌ {profile_name} profile invalid")
                return False
        
        # Test task settings
        tasks = ["guide_type", "mapping", "grading", "test"]
        for task in tasks:
            settings = get_task_settings(task)
            if isinstance(settings, dict) and 'max_tokens' in settings:
                print(f"✅ {task} settings loaded: {settings['max_tokens']} tokens")
            else:
                print(f"❌ {task} settings invalid")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Performance configuration test failed: {e}")
        return False

def main():
    """Run all basic functionality tests."""
    print("🧪 Basic Functionality Test Suite")
    print("=" * 50)
    
    tests = [
        test_directory_structure,
        test_template_files,
        test_config_import,
        test_upload_directory,
        test_flask_app,
        test_routes,
        test_css_files,
        test_performance_config
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ {test.__name__} failed")
        except Exception as e:
            print(f"❌ {test.__name__} crashed: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic functionality tests passed!")
        print("\n✅ The application structure is correct and ready to use.")
        print("✅ Upload functionality should work properly.")
        print("✅ All pages (Dashboard, Settings, Help) are accessible.")
        print("✅ Performance optimizations are configured.")
        print("\n🚀 You can now start the application with: python webapp/app.py")
    elif passed >= total * 0.8:  # 80% pass rate
        print("⚠️  Most tests passed, but some issues were found.")
        print("   The application should still be functional.")
    else:
        print("❌ Multiple tests failed. Please check the setup.")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    main()
