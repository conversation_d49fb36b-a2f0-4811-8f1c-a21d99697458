#!/usr/bin/env python3
"""
Validation script for codebase cleanup.
Ensures all imports work and core functionality is intact.
"""

import sys
import os
import importlib
from pathlib import Path

def test_imports():
    """Test that all core imports work correctly."""
    print("🧪 Testing Core Imports...")
    
    imports_to_test = [
        # Core services
        ('src.services.llm_service', 'LLMService'),
        ('src.services.mapping_service', 'MappingService'),
        ('src.services.grading_service', 'GradingService'),
        ('src.services.ocr_service', 'OCRService'),
        
        # Storage classes
        ('src.storage.guide_storage', 'GuideStorage'),
        ('src.storage.submission_storage', 'SubmissionStorage'),
        ('src.storage.results_storage', 'ResultsStorage'),
        ('src.storage.mapping_storage', 'MappingStorage'),
        
        # Parsing modules
        ('src.parsing.parse_guide', 'parse_marking_guide'),
        ('src.parsing.parse_submission', 'parse_student_submission'),
        
        # Config and utils
        ('src.config.config_manager', 'ConfigManager'),
        ('utils.logger', 'Logger'),
        ('utils.cache', 'Cache'),
    ]
    
    failed_imports = []
    
    for module_name, class_name in imports_to_test:
        try:
            module = importlib.import_module(module_name)
            if hasattr(module, class_name):
                print(f"✅ {module_name}.{class_name}")
            else:
                print(f"❌ {module_name}.{class_name} - Class not found")
                failed_imports.append(f"{module_name}.{class_name}")
        except ImportError as e:
            print(f"❌ {module_name}.{class_name} - Import error: {e}")
            failed_imports.append(f"{module_name}.{class_name}")
        except Exception as e:
            print(f"❌ {module_name}.{class_name} - Error: {e}")
            failed_imports.append(f"{module_name}.{class_name}")
    
    return len(failed_imports) == 0, failed_imports

def test_webapp_imports():
    """Test webapp imports."""
    print("\n🧪 Testing Webapp Imports...")
    
    try:
        # Add the project root to Python path
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        from webapp.app import create_app
        print("✅ webapp.app.create_app")
        
        # Try to create the app
        app = create_app()
        print("✅ Flask app creation")
        
        return True, []
    except Exception as e:
        print(f"❌ Webapp import failed: {e}")
        return False, [str(e)]

def test_file_structure():
    """Test that required files and directories exist."""
    print("\n🧪 Testing File Structure...")
    
    required_files = [
        'requirements.txt',
        'pyproject.toml',
        'run_app.py',
        'README.md',
        'src/services/llm_service.py',
        'webapp/app.py',
        'webapp/templates/index.html',
        'webapp/static/css/style.css',
        'webapp/static/js/main.js',
    ]
    
    required_dirs = [
        'src',
        'src/services',
        'src/storage',
        'src/parsing',
        'src/config',
        'webapp',
        'webapp/templates',
        'webapp/static',
        'tests',
        'utils',
    ]
    
    missing_files = []
    missing_dirs = []
    
    # Check files
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - Missing")
            missing_files.append(file_path)
    
    # Check directories
    for dir_path in required_dirs:
        if Path(dir_path).is_dir():
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ {dir_path}/ - Missing")
            missing_dirs.append(dir_path)
    
    return len(missing_files) == 0 and len(missing_dirs) == 0, missing_files + missing_dirs

def test_removed_files():
    """Test that removed files are actually gone."""
    print("\n🧪 Testing Removed Files...")
    
    should_be_removed = [
        'requirements_latest.txt',
        'debug_upload.html',
        'src/services/llm_service_latest.py',
        'test_upload_final.py',
        'test_minimal_app.py',
        'comprehensive_test.py',
        'mapping_test_result.json',
        'webapp/templates/mapping.html.bak',
        'setup.py',
        'run_app.bat',
    ]
    
    still_exist = []
    
    for file_path in should_be_removed:
        if Path(file_path).exists():
            print(f"❌ {file_path} - Should be removed")
            still_exist.append(file_path)
        else:
            print(f"✅ {file_path} - Correctly removed")
    
    return len(still_exist) == 0, still_exist

def test_requirements():
    """Test requirements.txt format."""
    print("\n🧪 Testing Requirements File...")
    
    try:
        with open('requirements.txt', 'r') as f:
            content = f.read()
        
        # Check for key dependencies
        required_deps = [
            'Flask>=3.0.2',
            'openai>=1.12.0',
            'PyMuPDF>=1.23.8',
            'python-docx>=1.1.0',
            'Pillow>=10.2.0',
        ]
        
        missing_deps = []
        for dep in required_deps:
            if dep.split('>=')[0] not in content:
                missing_deps.append(dep)
                print(f"❌ Missing dependency: {dep}")
            else:
                print(f"✅ Found dependency: {dep.split('>=')[0]}")
        
        return len(missing_deps) == 0, missing_deps
        
    except Exception as e:
        print(f"❌ Error reading requirements.txt: {e}")
        return False, [str(e)]

def main():
    """Run all validation tests."""
    print("🧹 Codebase Cleanup Validation")
    print("=" * 50)
    
    # Add project root to Python path
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    tests = [
        ("File Structure", test_file_structure),
        ("Removed Files", test_removed_files),
        ("Requirements", test_requirements),
        ("Core Imports", test_imports),
        ("Webapp Imports", test_webapp_imports),
    ]
    
    all_passed = True
    failed_tests = []
    
    for test_name, test_func in tests:
        try:
            passed, errors = test_func()
            if passed:
                print(f"\n✅ {test_name}: PASSED")
            else:
                print(f"\n❌ {test_name}: FAILED")
                print(f"   Errors: {errors}")
                all_passed = False
                failed_tests.append(test_name)
        except Exception as e:
            print(f"\n💥 {test_name}: CRASHED - {e}")
            all_passed = False
            failed_tests.append(test_name)
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All validation tests PASSED!")
        print("✅ Codebase cleanup was successful")
        print("\nNext steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Run tests: python -m pytest tests/")
        print("3. Start application: python run_app.py")
        return 0
    else:
        print("❌ Some validation tests FAILED!")
        print(f"Failed tests: {', '.join(failed_tests)}")
        print("\nPlease fix the issues before proceeding.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
