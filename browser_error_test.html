<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Error Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .error-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 0.9rem;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .iframe-container {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .dashboard-frame {
            width: 100%;
            height: 600px;
            border: none;
        }
        
        .instructions {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .error-count {
            display: inline-block;
            background: #dc3545;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            margin-left: 10px;
        }
        
        .warning-count {
            display: inline-block;
            background: #ffc107;
            color: #212529;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <h1>🔍 Dashboard Browser Error Test</h1>
    <p>This tool tests the dashboard for runtime JavaScript errors and console issues.</p>
    
    <div class="test-container">
        <h3>📊 Error Summary</h3>
        <div id="errorSummary" class="status warning">
            Initializing error monitoring...
        </div>
        
        <div>
            <button class="test-button" onclick="clearErrors()">Clear Errors</button>
            <button class="test-button" onclick="testDashboardFunctions()">Test Dashboard Functions</button>
            <button class="test-button" onclick="testUploadElements()">Test Upload Elements</button>
            <button class="test-button" onclick="refreshDashboard()">Refresh Dashboard</button>
        </div>
    </div>
    
    <div class="test-container">
        <h3>📝 Error Log <span id="errorCount" class="error-count" style="display: none;">0</span></h3>
        <div id="errorLog" class="error-log">
            <div style="color: #6c757d;">Monitoring for errors... Click on the dashboard below to test functionality.</div>
        </div>
    </div>
    
    <div class="test-container">
        <h3>🖥️ Dashboard Preview</h3>
        <div class="instructions">
            <strong>Instructions:</strong>
            <ol>
                <li>The dashboard is loaded in the frame below</li>
                <li>Try clicking on the upload boxes</li>
                <li>Check if file dialogs open</li>
                <li>Any JavaScript errors will appear in the error log above</li>
                <li>Use the test buttons to run specific tests</li>
            </ol>
        </div>
        
        <div class="iframe-container">
            <iframe id="dashboardFrame" class="dashboard-frame" src="http://127.0.0.1:8501"></iframe>
        </div>
    </div>
    
    <script>
        let errorCount = 0;
        let warningCount = 0;
        let errors = [];
        
        function logError(type, message, source = 'Browser Test') {
            errorCount++;
            const timestamp = new Date().toLocaleTimeString();
            const error = {
                type: type,
                message: message,
                source: source,
                timestamp: timestamp
            };
            errors.push(error);
            
            const errorLog = document.getElementById('errorLog');
            const errorEntry = document.createElement('div');
            errorEntry.style.marginBottom = '10px';
            errorEntry.style.padding = '8px';
            errorEntry.style.borderRadius = '4px';
            
            if (type === 'error') {
                errorEntry.style.background = '#f8d7da';
                errorEntry.style.color = '#721c24';
                errorEntry.innerHTML = `<strong>[${timestamp}] ERROR:</strong> ${message} <small>(${source})</small>`;
            } else if (type === 'warning') {
                errorEntry.style.background = '#fff3cd';
                errorEntry.style.color = '#856404';
                errorEntry.innerHTML = `<strong>[${timestamp}] WARNING:</strong> ${message} <small>(${source})</small>`;
            } else {
                errorEntry.style.background = '#d1ecf1';
                errorEntry.style.color = '#0c5460';
                errorEntry.innerHTML = `<strong>[${timestamp}] INFO:</strong> ${message} <small>(${source})</small>`;
            }
            
            errorLog.appendChild(errorEntry);
            errorLog.scrollTop = errorLog.scrollHeight;
            
            updateErrorSummary();
        }
        
        function updateErrorSummary() {
            const summary = document.getElementById('errorSummary');
            const errorCountElement = document.getElementById('errorCount');
            
            const errorCount = errors.filter(e => e.type === 'error').length;
            const warningCount = errors.filter(e => e.type === 'warning').length;
            
            if (errorCount > 0) {
                summary.className = 'status error';
                summary.textContent = `Found ${errorCount} error(s) and ${warningCount} warning(s)`;
            } else if (warningCount > 0) {
                summary.className = 'status warning';
                summary.textContent = `Found ${warningCount} warning(s), no critical errors`;
            } else {
                summary.className = 'status success';
                summary.textContent = 'No errors detected - Dashboard appears to be working correctly!';
            }
            
            if (errorCount > 0) {
                errorCountElement.textContent = errorCount;
                errorCountElement.style.display = 'inline-block';
            } else {
                errorCountElement.style.display = 'none';
            }
        }
        
        function clearErrors() {
            errors = [];
            errorCount = 0;
            warningCount = 0;
            document.getElementById('errorLog').innerHTML = '<div style="color: #6c757d;">Error log cleared. Monitoring for new errors...</div>';
            updateErrorSummary();
        }
        
        function testDashboardFunctions() {
            logError('info', 'Testing dashboard functions...', 'Test Runner');
            
            const iframe = document.getElementById('dashboardFrame');
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // Test if elements exist
                const guideDropzone = iframeDoc.getElementById('guideDropzone');
                const submissionDropzone = iframeDoc.getElementById('submissionDropzone');
                const guideFile = iframeDoc.getElementById('guideFile');
                const submissionFile = iframeDoc.getElementById('submissionFile');
                
                if (!guideDropzone) {
                    logError('error', 'Guide dropzone element not found', 'Element Test');
                } else {
                    logError('info', 'Guide dropzone element found', 'Element Test');
                }
                
                if (!submissionDropzone) {
                    logError('error', 'Submission dropzone element not found', 'Element Test');
                } else {
                    logError('info', 'Submission dropzone element found', 'Element Test');
                }
                
                if (!guideFile) {
                    logError('error', 'Guide file input not found', 'Element Test');
                } else {
                    logError('info', 'Guide file input found', 'Element Test');
                }
                
                if (!submissionFile) {
                    logError('error', 'Submission file input not found', 'Element Test');
                } else {
                    logError('info', 'Submission file input found', 'Element Test');
                }
                
                // Test if functions exist
                const iframeWindow = iframe.contentWindow;
                if (typeof iframeWindow.testLabelFunctionality === 'function') {
                    logError('info', 'testLabelFunctionality function found', 'Function Test');
                    try {
                        iframeWindow.testLabelFunctionality();
                        logError('info', 'testLabelFunctionality executed successfully', 'Function Test');
                    } catch (e) {
                        logError('error', `testLabelFunctionality failed: ${e.message}`, 'Function Test');
                    }
                } else {
                    logError('warning', 'testLabelFunctionality function not found', 'Function Test');
                }
                
            } catch (e) {
                logError('error', `Cannot access iframe content: ${e.message}`, 'Cross-Origin Test');
                logError('info', 'This is normal if dashboard is on different origin', 'Cross-Origin Test');
            }
        }
        
        function testUploadElements() {
            logError('info', 'Testing upload element functionality...', 'Upload Test');
            
            const iframe = document.getElementById('dashboardFrame');
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // Test clicking on upload elements
                const guideDropzone = iframeDoc.getElementById('guideDropzone');
                if (guideDropzone) {
                    logError('info', 'Attempting to click guide dropzone...', 'Upload Test');
                    guideDropzone.click();
                    logError('info', 'Guide dropzone click completed', 'Upload Test');
                } else {
                    logError('error', 'Cannot test guide dropzone - element not found', 'Upload Test');
                }
                
            } catch (e) {
                logError('warning', `Cannot test upload elements: ${e.message}`, 'Upload Test');
            }
        }
        
        function refreshDashboard() {
            logError('info', 'Refreshing dashboard...', 'Refresh');
            const iframe = document.getElementById('dashboardFrame');
            iframe.src = iframe.src;
        }
        
        // Monitor for JavaScript errors
        window.addEventListener('error', function(e) {
            logError('error', `${e.message} at ${e.filename}:${e.lineno}`, 'JavaScript Error');
        });
        
        // Monitor for unhandled promise rejections
        window.addEventListener('unhandledrejection', function(e) {
            logError('error', `Unhandled promise rejection: ${e.reason}`, 'Promise Rejection');
        });
        
        // Monitor iframe for errors
        document.addEventListener('DOMContentLoaded', function() {
            const iframe = document.getElementById('dashboardFrame');
            
            iframe.addEventListener('load', function() {
                logError('info', 'Dashboard loaded successfully', 'Iframe Load');
                
                try {
                    const iframeWindow = iframe.contentWindow;
                    
                    // Monitor iframe for errors
                    iframeWindow.addEventListener('error', function(e) {
                        logError('error', `Dashboard error: ${e.message}`, 'Dashboard JavaScript');
                    });
                    
                    iframeWindow.addEventListener('unhandledrejection', function(e) {
                        logError('error', `Dashboard promise rejection: ${e.reason}`, 'Dashboard Promise');
                    });
                    
                } catch (e) {
                    logError('info', 'Cannot monitor iframe errors due to cross-origin restrictions', 'Cross-Origin');
                }
            });
            
            iframe.addEventListener('error', function() {
                logError('error', 'Failed to load dashboard iframe', 'Iframe Error');
            });
            
            // Initial status
            updateErrorSummary();
            logError('info', 'Browser error monitoring started', 'Monitor');
        });
    </script>
</body>
</html>
