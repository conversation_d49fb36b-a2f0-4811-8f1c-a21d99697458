<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Clickable Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .upload-dropzone {
            border: 2px dashed #ccc;
            border-radius: 12px;
            padding: 2rem 1rem;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            min-height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            margin: 20px 0;
        }
        
        .upload-dropzone:hover {
            border-color: #4f46e5;
            background: #eef2ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
            border-style: solid;
        }
        
        .upload-dropzone:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            border-color: #3730a3;
        }
        
        .upload-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
        }
        
        .upload-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #e0e7ff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.25rem;
        }
        
        .upload-icon i {
            font-size: 1rem;
            color: #4f46e5;
        }
        
        .upload-text h6 {
            margin: 0 0 0.25rem 0;
            font-weight: 600;
            color: #374151;
            font-size: 0.9rem;
        }
        
        .upload-text p {
            margin: 0 0 0.25rem 0;
            color: #6b7280;
            font-size: 0.85rem;
        }
        
        .upload-link {
            color: #4f46e5;
            font-weight: 500;
            text-decoration: underline;
        }
        
        .upload-formats {
            color: #9ca3af;
            font-size: 0.75rem;
        }
        
        .test-result {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 1rem;
            margin: 20px 0;
        }
        
        .test-result.error {
            background: #fef2f2;
            border-color: #fecaca;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.8rem;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Upload Box Clickable Test</h1>
    <p>This test verifies that the upload boxes are fully clickable using the HTML label approach.</p>
    
    <div class="test-result" id="testResult">
        <strong>Test Status:</strong> <span id="testStatus">Ready to test</span>
    </div>
    
    <div class="log" id="log"></div>
    
    <!-- Test Upload Box 1 -->
    <h3>Guide Upload Test</h3>
    <form>
        <label for="guideFile" class="upload-dropzone">
            <input type="file" id="guideFile" name="file" accept=".docx,.txt" style="display: none;">
            <div class="upload-content">
                <div class="upload-icon">
                    <i>📄</i>
                </div>
                <div class="upload-text">
                    <h6>Drop your marking guide here</h6>
                    <p>or <span class="upload-link">click anywhere to browse</span></p>
                    <small class="upload-formats">Supports: .docx, .txt files</small>
                </div>
            </div>
        </label>
    </form>
    
    <!-- Test Upload Box 2 -->
    <h3>Submission Upload Test</h3>
    <form>
        <label for="submissionFile" class="upload-dropzone">
            <input type="file" id="submissionFile" name="file" accept=".docx,.txt,.pdf,.jpg,.jpeg,.png,.tiff,.bmp,.gif" multiple style="display: none;">
            <div class="upload-content">
                <div class="upload-icon">
                    <i>📁</i>
                </div>
                <div class="upload-text">
                    <h6>Drop student submissions here</h6>
                    <p>or <span class="upload-link">click anywhere to browse</span></p>
                    <small class="upload-formats">Supports: PDF, Word, Text, Images • Multiple files allowed</small>
                </div>
            </div>
        </label>
    </form>
    
    <script>
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateTestStatus(status, isError = false) {
            const statusElement = document.getElementById('testStatus');
            const resultElement = document.getElementById('testResult');
            
            statusElement.textContent = status;
            
            if (isError) {
                resultElement.className = 'test-result error';
            } else {
                resultElement.className = 'test-result';
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            log('Test page loaded');
            updateTestStatus('Testing upload functionality...');
            
            // Test guide file input
            const guideFileInput = document.getElementById('guideFile');
            if (guideFileInput) {
                guideFileInput.addEventListener('change', function() {
                    if (this.files.length > 0) {
                        const file = this.files[0];
                        log(`✅ Guide file selected: ${file.name} (${file.size} bytes)`);
                        updateTestStatus('Guide upload test PASSED');
                        
                        // Clear the input for repeated testing
                        setTimeout(() => {
                            this.value = '';
                            log('Guide input cleared for next test');
                        }, 1000);
                    }
                });
                log('Guide file input event listener attached');
            } else {
                log('❌ Guide file input not found');
                updateTestStatus('Guide file input not found', true);
            }
            
            // Test submission file input
            const submissionFileInput = document.getElementById('submissionFile');
            if (submissionFileInput) {
                submissionFileInput.addEventListener('change', function() {
                    if (this.files.length > 0) {
                        const files = Array.from(this.files);
                        log(`✅ Submission files selected: ${files.length} file(s)`);
                        files.forEach((file, index) => {
                            log(`   File ${index + 1}: ${file.name} (${file.size} bytes)`);
                        });
                        updateTestStatus('Submission upload test PASSED');
                        
                        // Clear the input for repeated testing
                        setTimeout(() => {
                            this.value = '';
                            log('Submission input cleared for next test');
                        }, 1000);
                    }
                });
                log('Submission file input event listener attached');
            } else {
                log('❌ Submission file input not found');
                updateTestStatus('Submission file input not found', true);
            }
            
            // Test label functionality
            const guideLabel = document.querySelector('label[for="guideFile"]');
            const submissionLabel = document.querySelector('label[for="submissionFile"]');
            
            if (guideLabel && submissionLabel) {
                log('✅ Both upload labels found');
                log('✅ Labels are properly associated with file inputs');
                updateTestStatus('Ready to test - click on upload boxes');
            } else {
                log('❌ Upload labels not found');
                updateTestStatus('Upload labels not found', true);
            }
            
            log('Test setup complete. Click on the upload boxes to test functionality.');
        });
    </script>
</body>
</html>
