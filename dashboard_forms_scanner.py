#!/usr/bin/env python3
"""
Comprehensive Dashboard Forms, Buttons, and Input Fields Scanner
Analyzes all interactive elements in the dashboard for proper configuration and functionality.
"""

import requests
import re
import json
import sys
from pathlib import Path

class DashboardFormsScanner:
    def __init__(self, base_url="http://127.0.0.1:8501"):
        self.base_url = base_url
        self.issues = []
        self.elements = {
            'forms': [],
            'buttons': [],
            'inputs': [],
            'selects': [],
            'labels': []
        }
        
    def log_issue(self, category, severity, message, element=None, details=None):
        """Log an issue with detailed information"""
        issue = {
            'category': category,
            'severity': severity,
            'message': message,
            'element': element,
            'details': details
        }
        self.issues.append(issue)
        
        icon = "❌" if severity == "error" else "⚠️" if severity == "warning" else "ℹ️"
        print(f"{icon} {severity.upper()} [{category}]: {message}")
        if element:
            print(f"   Element: {element}")
        if details:
            print(f"   Details: {details}")
    
    def get_dashboard_html(self):
        """Fetch dashboard HTML content"""
        try:
            response = requests.get(self.base_url, timeout=10)
            if response.status_code == 200:
                return response.text
            else:
                self.log_issue("CONNECTION", "error", f"Dashboard returned status {response.status_code}")
                return None
        except requests.exceptions.RequestException as e:
            self.log_issue("CONNECTION", "error", f"Cannot connect to dashboard: {e}")
            return None
    
    def extract_forms(self, html_content):
        """Extract and analyze all forms"""
        print("🔍 Analyzing Forms...")
        
        form_pattern = r'<form[^>]*>(.*?)</form>'
        forms = re.findall(form_pattern, html_content, re.DOTALL | re.IGNORECASE)
        
        # Extract form attributes
        form_attr_pattern = r'<form([^>]*)>'
        form_attrs = re.findall(form_attr_pattern, html_content, re.IGNORECASE)
        
        for i, (form_content, form_attr) in enumerate(zip(forms, form_attrs)):
            form_info = {
                'index': i + 1,
                'attributes': form_attr.strip(),
                'content': form_content.strip()[:200] + "..." if len(form_content) > 200 else form_content.strip()
            }
            
            # Check required attributes
            if 'action=' not in form_attr:
                self.log_issue("FORMS", "error", f"Form {i+1} missing action attribute", form_attr)
            
            if 'method=' not in form_attr:
                self.log_issue("FORMS", "warning", f"Form {i+1} missing method attribute", form_attr)
            elif 'method="post"' not in form_attr.lower():
                self.log_issue("FORMS", "warning", f"Form {i+1} not using POST method", form_attr)
            
            # Check for file upload forms
            if 'type="file"' in form_content and 'enctype="multipart/form-data"' not in form_attr:
                self.log_issue("FORMS", "error", f"Form {i+1} has file input but missing multipart encoding", form_attr)
            
            # Check for form ID
            if 'id=' not in form_attr:
                self.log_issue("FORMS", "warning", f"Form {i+1} missing ID attribute", form_attr)
            
            self.elements['forms'].append(form_info)
        
        print(f"   Found {len(forms)} forms")
        return forms
    
    def extract_buttons(self, html_content):
        """Extract and analyze all buttons"""
        print("🔍 Analyzing Buttons...")
        
        # Button elements
        button_pattern = r'<button([^>]*?)>(.*?)</button>'
        buttons = re.findall(button_pattern, html_content, re.DOTALL | re.IGNORECASE)
        
        # Input buttons
        input_button_pattern = r'<input([^>]*type=["\'](?:button|submit|reset)["\'][^>]*?)/?>'
        input_buttons = re.findall(input_button_pattern, html_content, re.IGNORECASE)
        
        all_buttons = []
        
        # Analyze button elements
        for i, (button_attr, button_content) in enumerate(buttons):
            button_info = {
                'type': 'button_element',
                'index': i + 1,
                'attributes': button_attr.strip(),
                'content': button_content.strip()[:100] + "..." if len(button_content) > 100 else button_content.strip()
            }
            
            # Check button type
            if 'type=' not in button_attr:
                self.log_issue("BUTTONS", "warning", f"Button {i+1} missing type attribute", button_attr)
            
            # Check for disabled state logic
            if 'disabled' in button_attr and '{{' not in button_attr:
                self.log_issue("BUTTONS", "info", f"Button {i+1} is statically disabled", button_attr)
            
            # Check for event handlers
            if 'onclick=' not in button_attr and 'data-' not in button_attr and 'type="submit"' not in button_attr:
                self.log_issue("BUTTONS", "warning", f"Button {i+1} may lack event handling", button_attr)
            
            # Check for accessibility
            if not button_content.strip() and 'aria-label=' not in button_attr:
                self.log_issue("BUTTONS", "warning", f"Button {i+1} missing accessible text", button_attr)
            
            all_buttons.append(button_info)
            self.elements['buttons'].append(button_info)
        
        # Analyze input buttons
        for i, input_attr in enumerate(input_buttons):
            button_info = {
                'type': 'input_button',
                'index': len(buttons) + i + 1,
                'attributes': input_attr.strip(),
                'content': 'N/A (input element)'
            }
            
            # Check for value attribute
            if 'value=' not in input_attr:
                self.log_issue("BUTTONS", "warning", f"Input button {i+1} missing value attribute", input_attr)
            
            all_buttons.append(button_info)
            self.elements['buttons'].append(button_info)
        
        print(f"   Found {len(buttons)} button elements and {len(input_buttons)} input buttons")
        return all_buttons
    
    def extract_inputs(self, html_content):
        """Extract and analyze all input fields"""
        print("🔍 Analyzing Input Fields...")
        
        input_pattern = r'<input([^>]*?)/?>'
        inputs = re.findall(input_pattern, html_content, re.IGNORECASE)
        
        for i, input_attr in enumerate(inputs):
            input_info = {
                'index': i + 1,
                'attributes': input_attr.strip()
            }
            
            # Check required attributes
            if 'type=' not in input_attr:
                self.log_issue("INPUTS", "error", f"Input {i+1} missing type attribute", input_attr)
            
            if 'name=' not in input_attr and 'type="button"' not in input_attr:
                self.log_issue("INPUTS", "warning", f"Input {i+1} missing name attribute", input_attr)
            
            # Check file inputs specifically
            if 'type="file"' in input_attr:
                if 'accept=' not in input_attr:
                    self.log_issue("INPUTS", "warning", f"File input {i+1} missing accept attribute", input_attr)
                
                if 'id=' not in input_attr:
                    self.log_issue("INPUTS", "error", f"File input {i+1} missing ID attribute", input_attr)
            
            # Check number inputs
            if 'type="number"' in input_attr:
                if 'min=' not in input_attr and 'max=' not in input_attr:
                    self.log_issue("INPUTS", "info", f"Number input {i+1} missing min/max constraints", input_attr)
            
            # Check for accessibility
            if 'id=' in input_attr:
                input_id = re.search(r'id=["\']([^"\']+)["\']', input_attr)
                if input_id:
                    label_pattern = f'<label[^>]*for=["\']?{input_id.group(1)}["\']?[^>]*>'
                    if not re.search(label_pattern, html_content, re.IGNORECASE):
                        self.log_issue("INPUTS", "warning", f"Input {i+1} missing associated label", input_attr)
            
            self.elements['inputs'].append(input_info)
        
        print(f"   Found {len(inputs)} input fields")
        return inputs
    
    def extract_selects(self, html_content):
        """Extract and analyze all select elements"""
        print("🔍 Analyzing Select Elements...")
        
        select_pattern = r'<select([^>]*?)>(.*?)</select>'
        selects = re.findall(select_pattern, html_content, re.DOTALL | re.IGNORECASE)
        
        for i, (select_attr, select_content) in enumerate(selects):
            select_info = {
                'index': i + 1,
                'attributes': select_attr.strip(),
                'content': select_content.strip()[:200] + "..." if len(select_content) > 200 else select_content.strip()
            }
            
            # Check required attributes
            if 'name=' not in select_attr:
                self.log_issue("SELECTS", "warning", f"Select {i+1} missing name attribute", select_attr)
            
            # Check for options
            option_count = len(re.findall(r'<option', select_content, re.IGNORECASE))
            if option_count == 0:
                self.log_issue("SELECTS", "error", f"Select {i+1} has no options", select_attr)
            
            # Check for accessibility
            if 'id=' in select_attr:
                select_id = re.search(r'id=["\']([^"\']+)["\']', select_attr)
                if select_id:
                    label_pattern = f'<label[^>]*for=["\']?{select_id.group(1)}["\']?[^>]*>'
                    if not re.search(label_pattern, html_content, re.IGNORECASE):
                        self.log_issue("SELECTS", "warning", f"Select {i+1} missing associated label", select_attr)
            
            self.elements['selects'].append(select_info)
        
        print(f"   Found {len(selects)} select elements")
        return selects
    
    def extract_labels(self, html_content):
        """Extract and analyze all label elements"""
        print("🔍 Analyzing Label Elements...")
        
        label_pattern = r'<label([^>]*?)>(.*?)</label>'
        labels = re.findall(label_pattern, html_content, re.DOTALL | re.IGNORECASE)
        
        for i, (label_attr, label_content) in enumerate(labels):
            label_info = {
                'index': i + 1,
                'attributes': label_attr.strip(),
                'content': label_content.strip()[:100] + "..." if len(label_content) > 100 else label_content.strip()
            }
            
            # Check for 'for' attribute
            if 'for=' not in label_attr:
                self.log_issue("LABELS", "warning", f"Label {i+1} missing 'for' attribute", label_attr)
            else:
                # Check if referenced element exists
                for_match = re.search(r'for=["\']([^"\']+)["\']', label_attr)
                if for_match:
                    target_id = for_match.group(1)
                    id_pattern = f'id=["\']?{target_id}["\']?'
                    if not re.search(id_pattern, html_content, re.IGNORECASE):
                        self.log_issue("LABELS", "error", f"Label {i+1} references non-existent element '{target_id}'", label_attr)
            
            self.elements['labels'].append(label_info)
        
        print(f"   Found {len(labels)} label elements")
        return labels
    
    def check_form_validation(self, html_content):
        """Check for form validation implementation"""
        print("🔍 Checking Form Validation...")
        
        # Check for HTML5 validation attributes
        validation_attrs = ['required', 'pattern', 'min', 'max', 'minlength', 'maxlength']
        found_validation = []
        
        for attr in validation_attrs:
            if attr in html_content:
                found_validation.append(attr)
        
        if found_validation:
            print(f"   Found HTML5 validation: {', '.join(found_validation)}")
        else:
            self.log_issue("VALIDATION", "info", "No HTML5 validation attributes found")
        
        # Check for JavaScript validation
        js_validation_patterns = [
            'validateFile',
            'validation',
            'checkForm',
            'validate'
        ]
        
        found_js_validation = []
        for pattern in js_validation_patterns:
            if pattern in html_content:
                found_js_validation.append(pattern)
        
        if found_js_validation:
            print(f"   Found JavaScript validation: {', '.join(found_js_validation)}")
        else:
            self.log_issue("VALIDATION", "warning", "No JavaScript validation functions found")
    
    def generate_report(self):
        """Generate comprehensive report"""
        print("\n" + "="*80)
        print("📊 DASHBOARD FORMS, BUTTONS & INPUTS ANALYSIS REPORT")
        print("="*80)
        
        # Summary
        error_count = len([i for i in self.issues if i['severity'] == 'error'])
        warning_count = len([i for i in self.issues if i['severity'] == 'warning'])
        info_count = len([i for i in self.issues if i['severity'] == 'info'])
        
        print(f"\n📈 SUMMARY:")
        print(f"   📝 Forms: {len(self.elements['forms'])}")
        print(f"   🔘 Buttons: {len(self.elements['buttons'])}")
        print(f"   📄 Inputs: {len(self.elements['inputs'])}")
        print(f"   📋 Selects: {len(self.elements['selects'])}")
        print(f"   🏷️  Labels: {len(self.elements['labels'])}")
        print(f"   ❌ Errors: {error_count}")
        print(f"   ⚠️  Warnings: {warning_count}")
        print(f"   ℹ️  Info: {info_count}")
        
        # Issues by category
        if self.issues:
            categories = {}
            for issue in self.issues:
                cat = issue['category']
                if cat not in categories:
                    categories[cat] = {'error': 0, 'warning': 0, 'info': 0}
                categories[cat][issue['severity']] += 1
            
            print(f"\n📊 ISSUES BY CATEGORY:")
            for cat, counts in categories.items():
                total = sum(counts.values())
                print(f"   {cat}: {total} total (❌{counts['error']} ⚠️{counts['warning']} ℹ️{counts['info']})")
        
        # Critical issues
        critical_issues = [i for i in self.issues if i['severity'] == 'error']
        if critical_issues:
            print(f"\n❌ CRITICAL ISSUES:")
            for issue in critical_issues:
                print(f"   • [{issue['category']}] {issue['message']}")
        
        return error_count == 0
    
    def run_full_scan(self):
        """Run complete forms analysis"""
        print("🚀 Starting Comprehensive Forms, Buttons & Inputs Analysis...")
        print("="*80)
        
        html_content = self.get_dashboard_html()
        if not html_content:
            return False
        
        # Run all analyses
        self.extract_forms(html_content)
        self.extract_buttons(html_content)
        self.extract_inputs(html_content)
        self.extract_selects(html_content)
        self.extract_labels(html_content)
        self.check_form_validation(html_content)
        
        # Generate report
        success = self.generate_report()
        
        # Save detailed report
        report_data = {
            'elements': self.elements,
            'issues': self.issues,
            'summary': {
                'total_forms': len(self.elements['forms']),
                'total_buttons': len(self.elements['buttons']),
                'total_inputs': len(self.elements['inputs']),
                'total_selects': len(self.elements['selects']),
                'total_labels': len(self.elements['labels']),
                'total_issues': len(self.issues),
                'critical_issues': len([i for i in self.issues if i['severity'] == 'error'])
            }
        }
        
        with open('dashboard_forms_report.json', 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: dashboard_forms_report.json")
        return success

def main():
    scanner = DashboardFormsScanner()
    success = scanner.run_full_scan()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
