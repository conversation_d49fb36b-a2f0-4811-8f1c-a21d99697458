{"errors": [], "warnings": [{"category": "CSS", "message": "CSS class not found in HTML: main-card", "details": null, "severity": "warning"}, {"category": "JAVASCRIPT", "message": "Function not found: testLabelFunctionality", "details": null, "severity": "warning"}, {"category": "JAVASCRIPT", "message": "Function not found: debugUploadElements", "details": null, "severity": "warning"}, {"category": "JAVASCRIPT", "message": "Function not found: preventDefaults", "details": null, "severity": "warning"}], "info": [{"category": "CONNECTION", "message": "Dashboard is accessible", "details": null, "severity": "info"}, {"category": "HTML_STRUCTURE", "message": "Found element: Guide upload dropzone", "details": null, "severity": "info"}, {"category": "HTML_STRUCTURE", "message": "Found element: Submission upload dropzone", "details": null, "severity": "info"}, {"category": "HTML_STRUCTURE", "message": "Found element: Guide file input", "details": null, "severity": "info"}, {"category": "HTML_STRUCTURE", "message": "Found element: Submission file input", "details": null, "severity": "info"}, {"category": "HTML_STRUCTURE", "message": "Found element: Guide upload form", "details": null, "severity": "info"}, {"category": "HTML_STRUCTURE", "message": "Found element: Submission upload form", "details": null, "severity": "info"}, {"category": "JAVASCRIPT", "message": "Found event handler: addEventListener", "details": null, "severity": "info"}, {"category": "FORMS", "message": "Found: Guide upload form action", "details": null, "severity": "info"}, {"category": "FORMS", "message": "Found: Submission upload form action", "details": null, "severity": "info"}, {"category": "FORMS", "message": "Found: POST method forms", "details": null, "severity": "info"}, {"category": "FORMS", "message": "Found: Multipart form encoding", "details": null, "severity": "info"}, {"category": "PERFORMANCE", "message": "Found 5 External CSS resources", "details": null, "severity": "info"}], "timestamp": "2025-05-24 13:19:53", "success": true}